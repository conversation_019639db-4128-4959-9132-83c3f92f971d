<?php
/**
 * The template for displaying 404 pages (not found)
 *
 * @package DoMovie
 * @since 1.0.0
 */

get_header(); ?>

<main class="main-content error-404-page">
    <div class="container">
        <div class="error-404-content">
            <div class="error-404-visual">
                <div class="error-code">404</div>
                <div class="error-icon">
                    <i class="fas fa-film"></i>
                </div>
            </div>
            
            <div class="error-404-text">
                <h1 class="error-title"><?php _e('Oops! Page Not Found', 'doomovie'); ?></h1>
                <p class="error-description">
                    <?php _e('The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.', 'doomovie'); ?>
                </p>
                
                <div class="error-actions">
                    <a href="<?php echo esc_url(home_url('/')); ?>" class="btn btn-primary">
                        <i class="fas fa-home"></i>
                        <?php _e('Go to Homepage', 'doomovie'); ?>
                    </a>
                    
                    <button class="btn btn-secondary" onclick="history.back()">
                        <i class="fas fa-arrow-left"></i>
                        <?php _e('Go Back', 'doomovie'); ?>
                    </button>
                </div>
            </div>
        </div>

        <!-- Search Section -->
        <div class="error-search-section">
            <h2><?php _e('Search for Movies', 'doomovie'); ?></h2>
            <p><?php _e('Try searching for what you were looking for:', 'doomovie'); ?></p>
            
            <form role="search" method="get" class="search-form" action="<?php echo esc_url(home_url('/')); ?>">
                <div class="search-input-wrapper">
                    <input type="search" 
                           class="search-field" 
                           placeholder="<?php _e('Search movies...', 'doomovie'); ?>" 
                           name="s" 
                           autocomplete="off">
                    <input type="hidden" name="post_type" value="movie">
                    <button type="submit" class="search-submit">
                        <i class="fas fa-search"></i>
                        <?php _e('Search', 'doomovie'); ?>
                    </button>
                </div>
            </form>
        </div>

        <!-- Popular Movies -->
        <?php
        $popular_movies = new WP_Query(array(
            'post_type' => 'movie',
            'posts_per_page' => 8,
            'meta_key' => '_view_count',
            'orderby' => 'meta_value_num',
            'order' => 'DESC'
        ));
        
        if ($popular_movies->have_posts()) :
        ?>
            <div class="popular-movies-section">
                <h2><?php _e('Popular Movies', 'doomovie'); ?></h2>
                <p><?php _e('Check out these popular movies instead:', 'doomovie'); ?></p>
                
                <div class="movies-grid">
                    <?php while ($popular_movies->have_posts()) : $popular_movies->the_post(); ?>
                        <?php get_template_part('template-parts/movie/movie-card'); ?>
                    <?php endwhile; ?>
                </div>
            </div>
        <?php 
        endif;
        wp_reset_postdata();
        ?>

        <!-- Latest Movies -->
        <?php
        $latest_movies = new WP_Query(array(
            'post_type' => 'movie',
            'posts_per_page' => 8,
            'orderby' => 'date',
            'order' => 'DESC'
        ));
        
        if ($latest_movies->have_posts()) :
        ?>
            <div class="latest-movies-section">
                <h2><?php _e('Latest Movies', 'doomovie'); ?></h2>
                <p><?php _e('Or browse our latest movie additions:', 'doomovie'); ?></p>
                
                <div class="movies-grid">
                    <?php while ($latest_movies->have_posts()) : $latest_movies->the_post(); ?>
                        <?php get_template_part('template-parts/movie/movie-card'); ?>
                    <?php endwhile; ?>
                </div>
            </div>
        <?php 
        endif;
        wp_reset_postdata();
        ?>

        <!-- Categories -->
        <div class="categories-section">
            <h2><?php _e('Browse by Category', 'doomovie'); ?></h2>
            <div class="categories-grid">
                <?php
                $genres = get_terms(array(
                    'taxonomy' => 'movie_genre',
                    'hide_empty' => true,
                    'number' => 12
                ));
                
                if (!empty($genres) && !is_wp_error($genres)) :
                    foreach ($genres as $genre) :
                ?>
                    <a href="<?php echo get_term_link($genre); ?>" class="category-card">
                        <div class="category-icon">
                            <i class="fas fa-film"></i>
                        </div>
                        <h3><?php echo esc_html($genre->name); ?></h3>
                        <span class="category-count">
                            <?php printf(_n('%d movie', '%d movies', $genre->count, 'doomovie'), $genre->count); ?>
                        </span>
                    </a>
                <?php 
                    endforeach;
                endif;
                ?>
            </div>
        </div>

        <!-- Help Section -->
        <div class="help-section">
            <h2><?php _e('Need Help?', 'doomovie'); ?></h2>
            <div class="help-grid">
                <div class="help-item">
                    <div class="help-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <h3><?php _e('FAQ', 'doomovie'); ?></h3>
                    <p><?php _e('Find answers to frequently asked questions.', 'doomovie'); ?></p>
                    <a href="<?php echo esc_url(home_url('/faq/')); ?>" class="help-link">
                        <?php _e('View FAQ', 'doomovie'); ?>
                    </a>
                </div>
                
                <div class="help-item">
                    <div class="help-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <h3><?php _e('Contact Us', 'doomovie'); ?></h3>
                    <p><?php _e('Get in touch with our support team.', 'doomovie'); ?></p>
                    <a href="<?php echo esc_url(home_url('/contact/')); ?>" class="help-link">
                        <?php _e('Contact Support', 'doomovie'); ?>
                    </a>
                </div>
                
                <div class="help-item">
                    <div class="help-icon">
                        <i class="fas fa-plus"></i>
                    </div>
                    <h3><?php _e('Request Movie', 'doomovie'); ?></h3>
                    <p><?php _e('Request a movie that you want to watch.', 'doomovie'); ?></p>
                    <a href="<?php echo esc_url(home_url('/request-movie/')); ?>" class="help-link">
                        <?php _e('Request Movie', 'doomovie'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</main>

<style>
.error-404-page {
    padding: 4rem 0;
    text-align: center;
}

.error-404-content {
    margin-bottom: 4rem;
}

.error-404-visual {
    margin-bottom: 2rem;
}

.error-code {
    font-size: 8rem;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
    margin-bottom: 1rem;
}

.error-icon {
    font-size: 4rem;
    color: var(--text-muted);
    margin-bottom: 2rem;
}

.error-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.error-description {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.error-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.error-search-section,
.popular-movies-section,
.latest-movies-section,
.categories-section,
.help-section {
    margin-bottom: 4rem;
    text-align: left;
}

.error-search-section {
    text-align: center;
}

.search-input-wrapper {
    display: flex;
    max-width: 500px;
    margin: 0 auto;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.search-field {
    flex: 1;
    padding: 1rem;
    border: none;
    background-color: var(--secondary-color);
    color: var(--text-primary);
    font-size: 1rem;
}

.search-field::placeholder {
    color: var(--text-muted);
}

.search-submit {
    padding: 1rem 1.5rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.search-submit:hover {
    background-color: #b8070f;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.category-card {
    background-color: var(--secondary-color);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    transition: var(--transition);
    text-decoration: none;
    color: var(--text-primary);
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
    color: var(--primary-color);
}

.category-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.category-count {
    color: var(--text-muted);
    font-size: 0.875rem;
}

.help-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.help-item {
    background-color: var(--secondary-color);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
}

.help-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.help-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.help-link:hover {
    text-decoration: underline;
}

@media (max-width: 768px) {
    .error-code {
        font-size: 6rem;
    }
    
    .error-title {
        font-size: 2rem;
    }
    
    .error-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .categories-grid,
    .help-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php get_footer(); ?>
