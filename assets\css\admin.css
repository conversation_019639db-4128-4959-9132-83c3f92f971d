/* DoMovie Admin Styles */

/* Movie Meta Boxes */
.movie-meta-box {
    background: #fff;
    border: 1px solid #e5e5e5;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.movie-meta-box .form-table {
    margin: 0;
}

.movie-meta-box .form-table th {
    width: 150px;
    padding: 15px 10px 15px 0;
    font-weight: 600;
}

.movie-meta-box .form-table td {
    padding: 15px 10px;
}

.movie-meta-box input[type="text"],
.movie-meta-box input[type="url"],
.movie-meta-box input[type="email"],
.movie-meta-box input[type="number"],
.movie-meta-box select,
.movie-meta-box textarea {
    width: 100%;
    max-width: 400px;
}

.movie-meta-box textarea {
    height: 80px;
    resize: vertical;
}

.movie-meta-box .large-text {
    max-width: 100%;
}

/* Movie Columns */
.column-poster {
    width: 60px;
}

.column-year {
    width: 80px;
}

.column-rating {
    width: 80px;
}

.column-quality {
    width: 80px;
}

.column-genres {
    width: 150px;
}

/* DoMovie Settings Pages */
.doomovie-settings-page {
    max-width: 1000px;
}

.doomovie-settings-page .form-table th {
    width: 200px;
    font-weight: 600;
}

.doomovie-settings-page .form-table td {
    padding: 15px 10px;
}

.doomovie-settings-page .description {
    font-style: italic;
    color: #666;
    margin-top: 5px;
}

/* Settings Tabs */
.nav-tab-wrapper {
    margin-bottom: 20px;
}

.nav-tab {
    background: #f1f1f1;
    border: 1px solid #ccc;
    color: #555;
    text-decoration: none;
    padding: 8px 12px;
    margin-right: 5px;
    border-bottom: none;
}

.nav-tab:hover {
    background: #e9e9e9;
    color: #333;
}

.nav-tab-active {
    background: #fff;
    border-bottom: 1px solid #fff;
    color: #000;
}

/* Movie Requests Table */
.movie-requests-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.movie-requests-table th,
.movie-requests-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.movie-requests-table th {
    background-color: #f9f9f9;
    font-weight: 600;
}

.movie-requests-table .status-pending {
    color: #d63638;
}

.movie-requests-table .status-approved {
    color: #00a32a;
}

.movie-requests-table .actions {
    white-space: nowrap;
}

.movie-requests-table .actions .button {
    margin-right: 5px;
}

/* Color Picker */
.color-picker-wrapper {
    position: relative;
    display: inline-block;
}

.color-picker-preview {
    width: 30px;
    height: 30px;
    border: 1px solid #ddd;
    border-radius: 3px;
    display: inline-block;
    vertical-align: middle;
    margin-right: 10px;
    cursor: pointer;
}

/* Media Upload */
.media-upload-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
}

.media-preview {
    max-width: 150px;
    max-height: 100px;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.media-upload-btn,
.media-remove-btn {
    margin-left: 10px;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #2196F3;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

/* Statistics Cards */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stats-card {
    background: #fff;
    border: 1px solid #e5e5e5;
    border-radius: 5px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.stats-card .number {
    font-size: 2em;
    font-weight: bold;
    color: #0073aa;
    display: block;
}

.stats-card .label {
    color: #666;
    font-size: 0.9em;
    margin-top: 5px;
}

/* Help Text */
.help-text {
    background: #f0f6fc;
    border: 1px solid #c3c4c7;
    border-left: 4px solid #72aee6;
    padding: 12px;
    margin: 15px 0;
}

.help-text p {
    margin: 0;
}

/* Error Messages */
.doomovie-error {
    background: #fcf2f2;
    border-left: 4px solid #d63638;
    padding: 12px;
    margin: 15px 0;
}

.doomovie-success {
    background: #f0f6fc;
    border-left: 4px solid #00a32a;
    padding: 12px;
    margin: 15px 0;
}

/* Responsive */
@media (max-width: 768px) {
    .movie-meta-box .form-table th {
        width: auto;
        display: block;
        padding-bottom: 5px;
    }
    
    .movie-meta-box .form-table td {
        display: block;
        padding-top: 5px;
    }
    
    .stats-cards {
        grid-template-columns: 1fr;
    }
    
    .media-upload-wrapper {
        flex-direction: column;
        align-items: flex-start;
    }
}
