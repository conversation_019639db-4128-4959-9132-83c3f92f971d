/* DoMovie Theme Main Styles - Inspired by EZMovie.me */

/* CSS Variables */
:root {
    --primary-color: #e50914;
    --secondary-color: #221f1f;
    --accent-color: #f5c518;
    --dark-bg: #141414;
    --darker-bg: #0f0f0f;
    --card-bg: #1a1a1a;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-muted: #808080;
    --border-color: #333333;
    --container-width: 1200px;
    --border-radius: 8px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    --shadow-hover: 0 8px 40px rgba(229, 9, 20, 0.3);
    --gradient-overlay: linear-gradient(135deg, rgba(0,0,0,0.8) 0%, rgba(229,9,20,0.1) 100%);
}

/* Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    background: var(--dark-bg);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    font-size: 14px;
}

.container {
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

a {
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-color);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #b8070f 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(229, 9, 20, 0.4);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(229, 9, 20, 0.6);
}

.btn-secondary {
    background: var(--card-bg);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--secondary-color);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

/* Navigation */
.main-navigation {
    background: rgba(15, 15, 15, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: var(--transition);
}

.main-navigation.scrolled {
    background: rgba(15, 15, 15, 0.98);
    box-shadow: var(--shadow);
}

.nav-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
}

.site-logo a {
    font-size: 1.8rem;
    font-weight: 800;
    color: var(--primary-color);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: var(--transition);
    position: relative;
}

.nav-menu a:hover,
.nav-menu .current-menu-item a {
    color: var(--primary-color);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition);
}

.nav-menu a:hover::after,
.nav-menu .current-menu-item a::after {
    width: 100%;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Search */
.search-wrapper {
    position: relative;
}

.search-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
    transition: var(--transition);
    border-radius: 50%;
}

.search-toggle:hover {
    color: var(--primary-color);
    background: rgba(229, 9, 20, 0.1);
}

.search-form-wrapper {
    position: absolute;
    top: 100%;
    right: 0;
    width: 350px;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
}

.search-form-wrapper.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.search-form {
    display: flex;
    padding: 0.5rem;
}

.search-field {
    flex: 1;
    background: none;
    border: none;
    color: var(--text-primary);
    padding: 0.75rem;
    outline: none;
    font-size: 14px;
}

.search-field::placeholder {
    color: var(--text-muted);
}

.search-submit {
    background: var(--primary-color);
    border: none;
    color: white;
    cursor: pointer;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.search-submit:hover {
    background: #b8070f;
}

/* Movie Cards */
.movie-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
    position: relative;
    border: 1px solid transparent;
}

.movie-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-hover);
    border-color: var(--primary-color);
}

.movie-poster {
    position: relative;
    aspect-ratio: 2/3;
    overflow: hidden;
    background: var(--darker-bg);
}

.poster-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.movie-card:hover .poster-image {
    transform: scale(1.1);
}

.movie-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-overlay);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.movie-card:hover .movie-overlay {
    opacity: 1;
}

.play-button {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-color) 0%, #b8070f 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.8rem;
    transition: var(--transition);
    box-shadow: 0 4px 20px rgba(229, 9, 20, 0.5);
}

.play-button:hover {
    transform: scale(1.15);
    box-shadow: 0 6px 30px rgba(229, 9, 20, 0.7);
}

.movie-info {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.movie-quality,
.imdb-rating {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.movie-quality {
    background: linear-gradient(135deg, var(--accent-color) 0%, #d4af00 100%);
    color: #000;
    box-shadow: 0 2px 10px rgba(245, 197, 24, 0.4);
}

.imdb-rating {
    background: rgba(0, 0, 0, 0.9);
    color: var(--accent-color);
    border: 1px solid var(--accent-color);
}

.movie-details {
    padding: 1.25rem;
}

.movie-title {
    font-size: 1rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
    line-height: 1.3;
}

.movie-title a {
    color: var(--text-primary);
    transition: var(--transition);
}

.movie-title a:hover {
    color: var(--primary-color);
}

.movie-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.75rem;
    font-size: 0.8rem;
    color: var(--text-muted);
}

.movie-rating {
    background: linear-gradient(135deg, var(--primary-color) 0%, #b8070f 100%);
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 700;
}

.movie-genres {
    display: flex;
    flex-wrap: wrap;
    gap: 0.4rem;
    margin-bottom: 0.75rem;
}

.genre-tag {
    background: var(--border-color);
    color: var(--text-secondary);
    padding: 0.2rem 0.6rem;
    border-radius: 15px;
    font-size: 0.7rem;
    font-weight: 500;
    transition: var(--transition);
}

.genre-tag:hover {
    background: var(--primary-color);
    color: white;
}

.movie-excerpt {
    font-size: 0.8rem;
    color: var(--text-muted);
    line-height: 1.4;
}

/* Grid Layouts */
.movies-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.movies-grid-large {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

.movies-grid-small {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 1.5rem;
}

/* Hero Slider */
.hero-slider-container {
    position: relative;
    height: 80vh;
    min-height: 600px;
    overflow: hidden;
    margin-bottom: 4rem;
    background: var(--darker-bg);
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    display: none;
}

.hero-slide.active {
    display: block;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(0,0,0,0.9) 0%,
        rgba(0,0,0,0.7) 40%,
        rgba(229,9,20,0.1) 70%,
        transparent 100%
    );
}

.hero-content {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    align-items: center;
    gap: 4rem;
    padding: 2rem 0;
}

.hero-info {
    flex: 1;
    max-width: 600px;
}

.hero-badges {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.quality-badge,
.imdb-badge,
.rating-badge {
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.quality-badge {
    background: linear-gradient(135deg, var(--accent-color) 0%, #d4af00 100%);
    color: #000;
    box-shadow: 0 4px 15px rgba(245, 197, 24, 0.4);
}

.imdb-badge {
    background: rgba(0, 0, 0, 0.9);
    color: var(--accent-color);
    border: 2px solid var(--accent-color);
}

.rating-badge {
    background: linear-gradient(135deg, var(--primary-color) 0%, #b8070f 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(229, 9, 20, 0.4);
}

.hero-title {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    line-height: 1.1;
    font-weight: 800;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-meta {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
    font-size: 1rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.hero-description {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2.5rem;
    color: var(--text-secondary);
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
}

.hero-actions {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.hero-poster {
    flex-shrink: 0;
}

.hero-poster-image {
    width: 350px;
    height: 525px;
    object-fit: cover;
    border-radius: var(--border-radius);
    box-shadow: 0 20px 60px rgba(0,0,0,0.8);
    transition: var(--transition);
}

.hero-poster-image:hover {
    transform: scale(1.05);
    box-shadow: 0 25px 80px rgba(229, 9, 20, 0.3);
}

/* Slider Navigation */
.slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 3;
}

.slider-prev {
    left: 2rem;
}

.slider-next {
    right: 2rem;
}

.slider-btn {
    width: 60px;
    height: 60px;
    background: rgba(0,0,0,0.7);
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    color: var(--primary-color);
    font-size: 1.5rem;
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.slider-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

/* Slider Indicators */
.slider-indicators {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 1rem;
    z-index: 3;
}

.slider-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255,255,255,0.5);
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.slider-indicator.active {
    background: var(--primary-color);
    transform: scale(1.3);
}

/* Sections */
.section-title {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    position: relative;
    padding-left: 1rem;
}

.section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    border-radius: 2px;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--primary-color);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--darker-bg);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #b8070f;
}

/* Mobile Menu */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    padding: 0.5rem;
    flex-direction: column;
    gap: 4px;
}

.hamburger-line {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: var(--transition);
    border-radius: 2px;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

.mobile-menu {
    position: fixed;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--darker-bg);
    border-top: 1px solid var(--border-color);
    transform: translateY(0);
    transition: var(--transition);
    z-index: 999;
    max-height: 0;
    overflow: hidden;
}

.mobile-menu.active {
    max-height: 100vh;
    transform: translateY(0);
}

.mobile-nav-menu {
    list-style: none;
    padding: 2rem;
}

.mobile-nav-menu li {
    margin-bottom: 1rem;
}

.mobile-nav-menu a {
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: block;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
}

.mobile-nav-menu a:hover {
    color: var(--primary-color);
    padding-left: 1rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .container {
        padding: 0 15px;
    }

    .hero-content {
        gap: 2rem;
    }

    .hero-poster-image {
        width: 280px;
        height: 420px;
    }
}

@media (max-width: 992px) {
    .nav-menu {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .hero-content {
        flex-direction: column;
        text-align: center;
        padding: 3rem 0;
    }

    .hero-title {
        font-size: 3rem;
    }

    .hero-poster-image {
        width: 250px;
        height: 375px;
    }

    .movies-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 1.5rem;
    }

    .section-title {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    body {
        font-size: 13px;
    }

    .container {
        padding: 0 10px;
    }

    .hero-slider-container {
        height: 60vh;
        min-height: 400px;
    }

    .hero-title {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .hero-description {
        font-size: 1rem;
        margin-bottom: 2rem;
    }

    .hero-actions {
        justify-content: center;
        gap: 1rem;
    }

    .hero-poster-image {
        width: 200px;
        height: 300px;
    }

    .movies-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 1rem;
    }

    .movie-details {
        padding: 1rem;
    }

    .movie-title {
        font-size: 0.9rem;
    }

    .btn {
        padding: 10px 20px;
        font-size: 12px;
    }

    .section-title {
        font-size: 1.8rem;
        margin-bottom: 1.5rem;
    }

    .slider-btn {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .slider-prev {
        left: 1rem;
    }

    .slider-next {
        right: 1rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-meta {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }

    .hero-actions {
        flex-direction: column;
        width: 100%;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }

    .movies-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .search-form-wrapper {
        width: 280px;
    }

    .hero-badges {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .quality-badge,
    .imdb-badge,
    .rating-badge {
        font-size: 0.7rem;
        padding: 0.3rem 0.8rem;
    }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
    :root {
        --shadow: 0 4px 20px rgba(0, 0, 0, 0.8);
        --shadow-hover: 0 8px 40px rgba(229, 9, 20, 0.5);
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    :root {
        --border-color: #666666;
        --text-muted: #cccccc;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    z-index: 10000;
    transform: translateX(400px);
    transition: var(--transition);
    max-width: 350px;
}

.notification.show {
    transform: translateX(0);
}

.notification-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
}

.notification-message {
    color: var(--text-primary);
    font-size: 0.9rem;
    font-weight: 500;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: 1.2rem;
    cursor: pointer;
    margin-left: 1rem;
    transition: var(--transition);
}

.notification-close:hover {
    color: var(--text-primary);
}

.notification-success {
    border-left: 4px solid #10b981;
}

.notification-error {
    border-left: 4px solid var(--primary-color);
}

.notification-info {
    border-left: 4px solid #3b82f6;
}

/* Tooltips */
.tooltip {
    position: absolute;
    background: var(--darker-bg);
    color: var(--text-primary);
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 10001;
    opacity: 0;
    transform: translateY(5px);
    transition: var(--transition);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow);
}

.tooltip.visible {
    opacity: 1;
    transform: translateY(0);
}

.tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--darker-bg);
}

/* Back to top button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color) 0%, #b8070f 100%);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: var(--transition);
    box-shadow: 0 4px 20px rgba(229, 9, 20, 0.4);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 30px rgba(229, 9, 20, 0.6);
}

/* Search results */
.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    max-height: 400px;
    overflow-y: auto;
    z-index: 1000;
}

.search-results-list {
    padding: 0.5rem 0;
}

.search-result-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 1rem;
    transition: var(--transition);
    cursor: pointer;
    animation: fadeInUp 0.3s ease-out;
}

.search-result-item:hover {
    background: var(--secondary-color);
}

.result-poster {
    flex-shrink: 0;
    width: 40px;
    height: 60px;
    border-radius: 4px;
    overflow: hidden;
}

.result-poster img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.result-info h4 {
    margin: 0 0 0.25rem 0;
    font-size: 0.9rem;
    font-weight: 600;
}

.result-info h4 a {
    color: var(--text-primary);
    text-decoration: none;
}

.result-info h4 a:hover {
    color: var(--primary-color);
}

.result-meta {
    display: flex;
    gap: 0.5rem;
    font-size: 0.7rem;
}

.result-meta .year {
    color: var(--text-muted);
}

.result-meta .quality {
    background: var(--accent-color);
    color: #000;
    padding: 0.1rem 0.4rem;
    border-radius: 8px;
    font-weight: 600;
}

.result-meta .rating {
    background: var(--primary-color);
    color: white;
    padding: 0.1rem 0.4rem;
    border-radius: 8px;
    font-weight: 600;
}

.search-loading,
.no-results,
.search-error {
    padding: 1rem;
    text-align: center;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.search-loading {
    color: var(--primary-color);
}

.search-error {
    color: var(--primary-color);
}

/* Movie actions */
.movie-actions {
    position: absolute;
    top: 1rem;
    left: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    opacity: 0;
    transform: translateX(-20px);
    transition: var(--transition);
}

.movie-card:hover .movie-actions {
    opacity: 1;
    transform: translateX(0);
}

.btn-favorite,
.btn-watch-later {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid var(--border-color);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.btn-favorite:hover,
.btn-watch-later:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.btn-favorite.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-favorite.loading,
.btn-watch-later.loading {
    pointer-events: none;
    opacity: 0.6;
}

/* Navigation enhancements */
.nav-hidden {
    transform: translateY(-100%);
}

.main-navigation {
    transition: transform 0.3s ease-in-out, background-color 0.3s ease;
}

/* Additional mobile styles */
@media (max-width: 480px) {
    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
        transform: translateY(-100px);
    }

    .notification.show {
        transform: translateY(0);
    }

    .back-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
    }

    .search-form-wrapper {
        left: 10px;
        right: 10px;
        width: auto;
    }
}
