/* DoMovie Theme Main Styles */

/* CSS Variables */
:root {
    --primary-color: #e50914;
    --secondary-color: #221f1f;
    --accent-color: #f5c518;
    --dark-bg: #141414;
    --darker-bg: #0f0f0f;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-muted: #808080;
    --border-color: #333333;
    --container-width: 1200px;
    --border-radius: 8px;
    --transition: all 0.3s ease;
    --shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    --shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.4);
}

/* Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--dark-bg);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

a {
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-color);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #b8070f;
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: #333;
    border-color: var(--primary-color);
}

.btn-outline {
    background-color: transparent;
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* Navigation */
.main-navigation {
    background-color: var(--darker-bg);
    border-bottom: 1px solid var(--border-color);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.nav-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
}

.site-logo a {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: var(--text-secondary);
    font-weight: 500;
    transition: var(--transition);
}

.nav-menu a:hover,
.nav-menu .current-menu-item a {
    color: var(--primary-color);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Search */
.search-wrapper {
    position: relative;
}

.search-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
    transition: var(--transition);
}

.search-toggle:hover {
    color: var(--primary-color);
}

.search-form-wrapper {
    position: absolute;
    top: 100%;
    right: 0;
    width: 300px;
    background-color: var(--secondary-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
}

.search-form-wrapper.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.search-form {
    display: flex;
    padding: 0.5rem;
}

.search-field {
    flex: 1;
    background: none;
    border: none;
    color: var(--text-primary);
    padding: 0.5rem;
    outline: none;
}

.search-field::placeholder {
    color: var(--text-muted);
}

.search-submit {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
}

/* Movie Cards */
.movie-card {
    background-color: var(--secondary-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
    position: relative;
}

.movie-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.movie-poster {
    position: relative;
    aspect-ratio: 2/3;
    overflow: hidden;
}

.poster-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.movie-card:hover .poster-image {
    transform: scale(1.05);
}

.movie-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.8) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.movie-card:hover .movie-overlay {
    opacity: 1;
}

.play-button {
    width: 60px;
    height: 60px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    transition: var(--transition);
}

.play-button:hover {
    transform: scale(1.1);
}

.movie-info {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.movie-quality,
.imdb-rating {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    text-align: center;
}

.movie-quality {
    background-color: var(--accent-color);
    color: black;
}

.imdb-rating {
    background-color: rgba(0, 0, 0, 0.8);
    color: var(--accent-color);
}

.movie-details {
    padding: 1rem;
}

.movie-title {
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.movie-title a {
    color: var(--text-primary);
}

.movie-meta {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-muted);
}

.movie-rating {
    background-color: var(--primary-color);
    color: white;
    padding: 0.125rem 0.375rem;
    border-radius: 4px;
    font-size: 0.75rem;
}

.movie-genres {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-bottom: 0.5rem;
}

.genre-tag {
    background-color: var(--border-color);
    color: var(--text-secondary);
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
}

.movie-excerpt {
    font-size: 0.875rem;
    color: var(--text-muted);
    line-height: 1.4;
}

/* Grid Layouts */
.movies-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.movies-grid-large {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

/* Hero Slider */
.hero-slider-container {
    position: relative;
    height: 70vh;
    min-height: 500px;
    overflow: hidden;
    margin-bottom: 3rem;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    display: none;
}

.hero-slide.active {
    display: block;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to right, rgba(0,0,0,0.8) 0%, transparent 100%);
}

.hero-content {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    align-items: center;
    gap: 3rem;
}

.hero-info {
    flex: 1;
    max-width: 600px;
}

.hero-badges {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.quality-badge,
.imdb-badge,
.rating-badge {
    padding: 0.375rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 600;
}

.quality-badge {
    background-color: var(--accent-color);
    color: black;
}

.imdb-badge {
    background-color: rgba(0, 0, 0, 0.8);
    color: var(--accent-color);
}

.rating-badge {
    background-color: var(--primary-color);
    color: white;
}

.hero-title {
    font-size: 3rem;
    margin-bottom: 1rem;
    line-height: 1.1;
}

.hero-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.hero-description {
    font-size: 1.125rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: var(--text-secondary);
}

.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-poster {
    flex-shrink: 0;
}

.hero-poster-image {
    width: 300px;
    height: 450px;
    object-fit: cover;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .nav-menu {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: block;
        background: none;
        border: none;
        color: var(--text-primary);
        cursor: pointer;
        padding: 0.5rem;
    }
    
    .movies-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 1rem;
    }
    
    .hero-content {
        flex-direction: column;
        text-align: center;
        padding: 2rem 0;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-poster-image {
        width: 200px;
        height: 300px;
    }
    
    .hero-actions {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .movies-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .hero-title {
        font-size: 1.5rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }
}
