/**
 * DoMovie Theme Main JavaScript
 */

(function($) {
    'use strict';

    // DOM Ready
    $(document).ready(function() {
        initializeTheme();
    });

    /**
     * Initialize theme functionality
     */
    function initializeTheme() {
        initNavigation();
        initSearch();
        initMovieActions();
        initSliders();
        initLoadMore();
        initScrollEffects();
        initTooltips();
    }

    /**
     * Navigation functionality
     */
    function initNavigation() {
        // Mobile menu toggle
        $('.mobile-menu-toggle').on('click', function() {
            $(this).toggleClass('active');
            $('.mobile-menu').toggleClass('active');
            $('body').toggleClass('mobile-menu-open');
        });

        // User dropdown
        $('.user-toggle').on('click', function(e) {
            e.preventDefault();
            $('.user-dropdown-menu').toggleClass('active');
        });

        // Close dropdowns when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.user-dropdown').length) {
                $('.user-dropdown-menu').removeClass('active');
            }
            if (!$(e.target).closest('.search-wrapper').length) {
                $('.search-form-wrapper').removeClass('active');
            }
        });

        // Sticky navigation
        $(window).on('scroll', function() {
            const nav = $('.main-navigation');
            if ($(window).scrollTop() > 100) {
                nav.addClass('scrolled');
            } else {
                nav.removeClass('scrolled');
            }
        });
    }

    /**
     * Search functionality
     */
    function initSearch() {
        let searchTimeout;
        
        // Search toggle
        $('.search-toggle').on('click', function() {
            $('.search-form-wrapper').toggleClass('active');
            if ($('.search-form-wrapper').hasClass('active')) {
                $('.search-field').focus();
            }
        });

        // Live search
        $('.search-field').on('input', function() {
            clearTimeout(searchTimeout);
            const query = $(this).val().trim();
            
            if (query.length >= 3) {
                searchTimeout = setTimeout(() => {
                    performLiveSearch(query);
                }, 300);
            } else {
                $('.search-results').empty().hide();
            }
        });

        // Perform live search
        function performLiveSearch(query) {
            $.ajax({
                url: doomovie_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'search_movies',
                    search_term: query,
                    nonce: doomovie_ajax.nonce
                },
                success: function(response) {
                    if (response.success && response.data.length > 0) {
                        displaySearchResults(response.data);
                    } else {
                        $('.search-results').html('<div class="no-results">No movies found</div>').show();
                    }
                },
                error: function() {
                    $('.search-results').html('<div class="search-error">Search error occurred</div>').show();
                }
            });
        }

        // Display search results
        function displaySearchResults(results) {
            let html = '';
            results.forEach(movie => {
                html += `
                    <div class="search-result-item">
                        <img src="${movie.poster}" alt="${movie.title}" class="result-poster">
                        <div class="result-info">
                            <h4><a href="${movie.url}">${movie.title}</a></h4>
                            <div class="result-meta">
                                ${movie.year ? `<span>${movie.year}</span>` : ''}
                                ${movie.quality ? `<span class="quality">${movie.quality}</span>` : ''}
                                ${movie.rating ? `<span class="rating">${movie.rating}</span>` : ''}
                            </div>
                        </div>
                    </div>
                `;
            });
            $('.search-results').html(html).show();
        }
    }

    /**
     * Movie actions (favorites, watch later, rating)
     */
    function initMovieActions() {
        // Add to favorites
        $(document).on('click', '.btn-favorite', function(e) {
            e.preventDefault();
            const $btn = $(this);
            const movieId = $btn.data('movie-id');
            
            if ($btn.hasClass('active')) {
                removeFromFavorites(movieId, $btn);
            } else {
                addToFavorites(movieId, $btn);
            }
        });

        // Add to watch later
        $(document).on('click', '.btn-watch-later', function(e) {
            e.preventDefault();
            const $btn = $(this);
            const movieId = $btn.data('movie-id');
            
            addToWatchLater(movieId, $btn);
        });

        // Movie rating
        $(document).on('click', '.rating-star', function() {
            const $star = $(this);
            const rating = $star.data('rating');
            const movieId = $star.closest('.movie-rating').data('movie-id');
            
            rateMovie(movieId, rating, $star);
        });
    }

    /**
     * Add movie to favorites
     */
    function addToFavorites(movieId, $btn) {
        $.ajax({
            url: doomovie_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'add_to_favorites',
                movie_id: movieId,
                nonce: doomovie_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    $btn.addClass('active');
                    showNotification('Added to favorites', 'success');
                } else {
                    showNotification(response.data, 'error');
                }
            },
            error: function() {
                showNotification('Error adding to favorites', 'error');
            }
        });
    }

    /**
     * Remove movie from favorites
     */
    function removeFromFavorites(movieId, $btn) {
        $.ajax({
            url: doomovie_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'remove_from_favorites',
                movie_id: movieId,
                nonce: doomovie_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    $btn.removeClass('active');
                    showNotification('Removed from favorites', 'success');
                } else {
                    showNotification(response.data, 'error');
                }
            },
            error: function() {
                showNotification('Error removing from favorites', 'error');
            }
        });
    }

    /**
     * Add movie to watch later
     */
    function addToWatchLater(movieId, $btn) {
        $.ajax({
            url: doomovie_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'add_to_watch_later',
                movie_id: movieId,
                nonce: doomovie_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    $btn.addClass('active');
                    showNotification('Added to watch later', 'success');
                } else {
                    showNotification(response.data, 'error');
                }
            },
            error: function() {
                showNotification('Error adding to watch later', 'error');
            }
        });
    }

    /**
     * Rate movie
     */
    function rateMovie(movieId, rating, $star) {
        $.ajax({
            url: doomovie_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'rate_movie',
                movie_id: movieId,
                rating: rating,
                nonce: doomovie_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    updateRatingDisplay($star, rating, response.data);
                    showNotification('Rating saved', 'success');
                } else {
                    showNotification(response.data, 'error');
                }
            },
            error: function() {
                showNotification('Error saving rating', 'error');
            }
        });
    }

    /**
     * Update rating display
     */
    function updateRatingDisplay($star, rating, data) {
        const $container = $star.closest('.movie-rating');
        $container.find('.rating-star').each(function(index) {
            $(this).toggleClass('active', index < rating);
        });
        
        if (data.average) {
            $container.find('.average-rating').text(data.average);
        }
        if (data.count) {
            $container.find('.rating-count').text(`(${data.count})`);
        }
    }

    /**
     * Initialize sliders
     */
    function initSliders() {
        // Hero slider
        if ($('.hero-slider').length) {
            initHeroSlider();
        }

        // Movie carousels
        $('.movie-carousel').each(function() {
            initMovieCarousel($(this));
        });
    }

    /**
     * Initialize hero slider
     */
    function initHeroSlider() {
        const $slider = $('.hero-slider');
        const $slides = $slider.find('.hero-slide');
        const $indicators = $('.slider-indicator');
        let currentSlide = 0;
        const totalSlides = $slides.length;

        function showSlide(index) {
            $slides.removeClass('active').eq(index).addClass('active');
            $indicators.removeClass('active').eq(index).addClass('active');
            currentSlide = index;
        }

        function nextSlide() {
            currentSlide = (currentSlide + 1) % totalSlides;
            showSlide(currentSlide);
        }

        function prevSlide() {
            currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
            showSlide(currentSlide);
        }

        // Navigation buttons
        $('.slider-next').on('click', nextSlide);
        $('.slider-prev').on('click', prevSlide);

        // Indicators
        $indicators.on('click', function() {
            const index = $(this).data('slide');
            showSlide(index);
        });

        // Auto-play
        setInterval(nextSlide, 5000);

        // Initialize
        showSlide(0);
    }

    /**
     * Initialize load more functionality
     */
    function initLoadMore() {
        $('.load-more-btn').on('click', function(e) {
            e.preventDefault();
            const $btn = $(this);
            const page = parseInt($btn.data('page')) + 1;
            const postsPerPage = $btn.data('posts-per-page') || 12;
            const category = $btn.data('category') || '';
            const genre = $btn.data('genre') || '';
            const year = $btn.data('year') || '';

            $btn.addClass('loading').text('Loading...');

            $.ajax({
                url: doomovie_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'load_more_movies',
                    page: page,
                    posts_per_page: postsPerPage,
                    category: category,
                    genre: genre,
                    year: year,
                    nonce: doomovie_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $('.movies-grid').append(response.data.html);
                        $btn.data('page', page);
                        
                        if (!response.data.has_more) {
                            $btn.hide();
                        }
                    } else {
                        showNotification('No more movies to load', 'info');
                        $btn.hide();
                    }
                },
                error: function() {
                    showNotification('Error loading movies', 'error');
                },
                complete: function() {
                    $btn.removeClass('loading').text('Load More');
                }
            });
        });
    }

    /**
     * Initialize scroll effects
     */
    function initScrollEffects() {
        // Smooth scroll for anchor links
        $('a[href^="#"]').on('click', function(e) {
            e.preventDefault();
            const target = $($(this).attr('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 500);
            }
        });

        // Back to top button
        const $backToTop = $('.back-to-top');
        $(window).on('scroll', function() {
            if ($(window).scrollTop() > 500) {
                $backToTop.fadeIn();
            } else {
                $backToTop.fadeOut();
            }
        });

        $backToTop.on('click', function(e) {
            e.preventDefault();
            $('html, body').animate({ scrollTop: 0 }, 500);
        });
    }

    /**
     * Initialize tooltips
     */
    function initTooltips() {
        $('[data-tooltip]').each(function() {
            const $element = $(this);
            const text = $element.data('tooltip');
            
            $element.on('mouseenter', function() {
                const $tooltip = $('<div class="tooltip">' + text + '</div>');
                $('body').append($tooltip);
                
                const offset = $element.offset();
                $tooltip.css({
                    top: offset.top - $tooltip.outerHeight() - 10,
                    left: offset.left + ($element.outerWidth() / 2) - ($tooltip.outerWidth() / 2)
                });
            });
            
            $element.on('mouseleave', function() {
                $('.tooltip').remove();
            });
        });
    }

    /**
     * Show notification
     */
    function showNotification(message, type = 'info') {
        const $notification = $(`
            <div class="notification notification-${type}">
                <span class="notification-message">${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `);

        $('body').append($notification);
        
        setTimeout(() => {
            $notification.addClass('show');
        }, 100);

        // Auto hide after 3 seconds
        setTimeout(() => {
            $notification.removeClass('show');
            setTimeout(() => {
                $notification.remove();
            }, 300);
        }, 3000);

        // Manual close
        $notification.find('.notification-close').on('click', function() {
            $notification.removeClass('show');
            setTimeout(() => {
                $notification.remove();
            }, 300);
        });
    }

})(jQuery);
