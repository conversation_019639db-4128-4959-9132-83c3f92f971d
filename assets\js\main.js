/**
 * DoMovie Theme Main JavaScript - Modern & Responsive
 * Inspired by EZMovie.me functionality
 */

(function($) {
    'use strict';

    // Theme configuration
    const DoMovie = {
        config: {
            breakpoints: {
                mobile: 768,
                tablet: 992,
                desktop: 1200
            },
            animations: {
                duration: 300,
                easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
            }
        },
        
        // Initialize theme
        init() {
            this.initNavigation();
            this.initSearch();
            this.initMovieActions();
            this.initSliders();
            this.initLoadMore();
            this.initScrollEffects();
            this.initTooltips();
            this.initLazyLoading();
            this.initKeyboardNavigation();
            this.initAdvancedAnimations();
            this.initParticleEffects();
            this.initMouseTrail();
            this.initLoadingAnimations();
            this.initPerformanceOptimizations();
        },

        /**
         * Navigation functionality
         */
        initNavigation() {
            const $nav = $('.main-navigation');
            const $mobileToggle = $('.mobile-menu-toggle');
            const $mobileMenu = $('.mobile-menu');
            const $userToggle = $('.user-toggle');
            const $userDropdown = $('.user-dropdown-menu');

            // Mobile menu toggle
            $mobileToggle.on('click', function() {
                $(this).toggleClass('active');
                $mobileMenu.toggleClass('active');
                $('body').toggleClass('mobile-menu-open');
                
                // Prevent scroll when menu is open
                if ($mobileMenu.hasClass('active')) {
                    $('body').css('overflow', 'hidden');
                } else {
                    $('body').css('overflow', '');
                }
            });

            // User dropdown
            $userToggle.on('click', function(e) {
                e.preventDefault();
                $userDropdown.toggleClass('active');
            });

            // Sticky navigation with scroll effect
            let lastScrollTop = 0;
            $(window).on('scroll', function() {
                const scrollTop = $(this).scrollTop();
                
                if (scrollTop > 100) {
                    $nav.addClass('scrolled');
                } else {
                    $nav.removeClass('scrolled');
                }

                // Hide/show nav on scroll
                if (scrollTop > lastScrollTop && scrollTop > 200) {
                    $nav.addClass('nav-hidden');
                } else {
                    $nav.removeClass('nav-hidden');
                }
                lastScrollTop = scrollTop;
            });

            // Close dropdowns when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.user-dropdown').length) {
                    $userDropdown.removeClass('active');
                }
                if (!$(e.target).closest('.search-wrapper').length) {
                    $('.search-form-wrapper').removeClass('active');
                }
                if (!$(e.target).closest('.mobile-menu, .mobile-menu-toggle').length) {
                    $mobileMenu.removeClass('active');
                    $mobileToggle.removeClass('active');
                    $('body').removeClass('mobile-menu-open').css('overflow', '');
                }
            });

            // Smooth scroll for anchor links
            $('a[href^="#"]').on('click', function(e) {
                e.preventDefault();
                const target = $($(this).attr('href'));
                if (target.length) {
                    $('html, body').animate({
                        scrollTop: target.offset().top - 100
                    }, 500);
                }
            });
        },

        /**
         * Enhanced search functionality
         */
        initSearch() {
            const $searchToggle = $('.search-toggle');
            const $searchWrapper = $('.search-form-wrapper');
            const $searchField = $('.search-field');
            const $searchResults = $('.search-results');
            let searchTimeout;
            
            // Search toggle with animation
            $searchToggle.on('click', function() {
                $searchWrapper.toggleClass('active');
                if ($searchWrapper.hasClass('active')) {
                    setTimeout(() => $searchField.focus(), 100);
                }
            });

            // Live search with debouncing
            $searchField.on('input', function() {
                clearTimeout(searchTimeout);
                const query = $(this).val().trim();
                
                if (query.length >= 2) {
                    searchTimeout = setTimeout(() => {
                        DoMovie.performLiveSearch(query);
                    }, 300);
                } else {
                    $searchResults.empty().hide();
                }
            });

            // Search suggestions
            $searchField.on('focus', function() {
                if ($(this).val().length >= 2) {
                    $searchResults.show();
                }
            });
        },

        /**
         * Perform live search with AJAX
         */
        performLiveSearch(query) {
            const $searchResults = $('.search-results');
            
            // Show loading state
            $searchResults.html('<div class="search-loading">Searching...</div>').show();
            
            $.ajax({
                url: doomovie_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'search_movies',
                    search_term: query,
                    limit: 8,
                    nonce: doomovie_ajax.nonce
                },
                success: function(response) {
                    if (response.success && response.data.length > 0) {
                        DoMovie.displaySearchResults(response.data);
                    } else {
                        $searchResults.html('<div class="no-results">No movies found</div>');
                    }
                },
                error: function() {
                    $searchResults.html('<div class="search-error">Search error occurred</div>');
                }
            });
        },

        /**
         * Display search results with animation
         */
        displaySearchResults(results) {
            const $searchResults = $('.search-results');
            let html = '<div class="search-results-list">';
            
            results.forEach((movie, index) => {
                html += `
                    <div class="search-result-item" style="animation-delay: ${index * 50}ms">
                        <div class="result-poster">
                            <img src="${movie.poster || '/wp-content/themes/doomovie/assets/images/no-poster.jpg'}" 
                                 alt="${movie.title}" 
                                 loading="lazy">
                        </div>
                        <div class="result-info">
                            <h4><a href="${movie.url}">${movie.title}</a></h4>
                            <div class="result-meta">
                                ${movie.year ? `<span class="year">${movie.year}</span>` : ''}
                                ${movie.quality ? `<span class="quality">${movie.quality}</span>` : ''}
                                ${movie.rating ? `<span class="rating">${movie.rating}</span>` : ''}
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            $searchResults.html(html).show();
            
            // Add click tracking
            $searchResults.find('.search-result-item').on('click', function() {
                const title = $(this).find('h4 a').text();
                DoMovie.trackEvent('search_result_click', { query: $('.search-field').val(), title: title });
            });
        },

        /**
         * Movie actions (favorites, watch later, rating)
         */
        initMovieActions() {
            // Favorite button
            $(document).on('click', '.btn-favorite', function(e) {
                e.preventDefault();
                const $btn = $(this);
                const movieId = $btn.data('movie-id');
                
                if ($btn.hasClass('loading')) return;
                
                $btn.addClass('loading');
                
                if ($btn.hasClass('active')) {
                    DoMovie.removeFromFavorites(movieId, $btn);
                } else {
                    DoMovie.addToFavorites(movieId, $btn);
                }
            });

            // Watch later button
            $(document).on('click', '.btn-watch-later', function(e) {
                e.preventDefault();
                const $btn = $(this);
                const movieId = $btn.data('movie-id');
                
                if ($btn.hasClass('loading')) return;
                
                $btn.addClass('loading');
                DoMovie.addToWatchLater(movieId, $btn);
            });

            // Movie rating
            $(document).on('click', '.rating-star', function() {
                const $star = $(this);
                const rating = $star.data('rating');
                const movieId = $star.closest('.movie-rating').data('movie-id');
                
                DoMovie.rateMovie(movieId, rating, $star);
            });

            // Movie card hover effects
            $('.movie-card').on('mouseenter', function() {
                $(this).addClass('hovered');
            }).on('mouseleave', function() {
                $(this).removeClass('hovered');
            });
        },

        /**
         * Add movie to favorites
         */
        addToFavorites(movieId, $btn) {
            $.ajax({
                url: doomovie_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'add_to_favorites',
                    movie_id: movieId,
                    nonce: doomovie_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $btn.addClass('active');
                        DoMovie.showNotification(doomovie_ajax.strings.added_to_favorites, 'success');
                        DoMovie.trackEvent('add_to_favorites', { movie_id: movieId });
                    } else {
                        DoMovie.showNotification(response.data, 'error');
                    }
                },
                error: function() {
                    DoMovie.showNotification('Error adding to favorites', 'error');
                },
                complete: function() {
                    $btn.removeClass('loading');
                }
            });
        },

        /**
         * Remove movie from favorites
         */
        removeFromFavorites(movieId, $btn) {
            $.ajax({
                url: doomovie_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'remove_from_favorites',
                    movie_id: movieId,
                    nonce: doomovie_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $btn.removeClass('active');
                        DoMovie.showNotification(doomovie_ajax.strings.removed_from_favorites, 'success');
                        DoMovie.trackEvent('remove_from_favorites', { movie_id: movieId });
                    } else {
                        DoMovie.showNotification(response.data, 'error');
                    }
                },
                error: function() {
                    DoMovie.showNotification('Error removing from favorites', 'error');
                },
                complete: function() {
                    $btn.removeClass('loading');
                }
            });
        }
    };

    // DOM Ready
    $(document).ready(function() {
        DoMovie.init();
    });

        /**
         * Initialize hero slider
         */
        initSliders() {
            const $slider = $('.hero-slider');
            const $slides = $slider.find('.hero-slide');
            const $indicators = $('.slider-indicator');
            const $prevBtn = $('.slider-prev');
            const $nextBtn = $('.slider-next');

            if ($slides.length === 0) return;

            let currentSlide = 0;
            const totalSlides = $slides.length;
            let autoplayInterval;

            function showSlide(index) {
                $slides.removeClass('active').eq(index).addClass('active');
                $indicators.removeClass('active').eq(index).addClass('active');
                currentSlide = index;
            }

            function nextSlide() {
                currentSlide = (currentSlide + 1) % totalSlides;
                showSlide(currentSlide);
            }

            function prevSlide() {
                currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
                showSlide(currentSlide);
            }

            function startAutoplay() {
                autoplayInterval = setInterval(nextSlide, 5000);
            }

            function stopAutoplay() {
                clearInterval(autoplayInterval);
            }

            // Navigation buttons
            $nextBtn.on('click', function() {
                nextSlide();
                stopAutoplay();
                startAutoplay();
            });

            $prevBtn.on('click', function() {
                prevSlide();
                stopAutoplay();
                startAutoplay();
            });

            // Indicators
            $indicators.on('click', function() {
                const index = $(this).data('slide');
                showSlide(index);
                stopAutoplay();
                startAutoplay();
            });

            // Keyboard navigation
            $(document).on('keydown', function(e) {
                if (e.key === 'ArrowLeft') {
                    prevSlide();
                } else if (e.key === 'ArrowRight') {
                    nextSlide();
                }
            });

            // Pause on hover
            $slider.on('mouseenter', stopAutoplay).on('mouseleave', startAutoplay);

            // Initialize
            showSlide(0);
            startAutoplay();
        },

        /**
         * Load more functionality
         */
        initLoadMore() {
            $(document).on('click', '.load-more-btn', function(e) {
                e.preventDefault();
                const $btn = $(this);
                const page = parseInt($btn.data('page')) + 1;
                const maxPages = parseInt($btn.data('max-pages'));
                const postsPerPage = $btn.data('posts-per-page') || 12;
                const category = $btn.data('category') || '';
                const genre = $btn.data('genre') || '';
                const year = $btn.data('year') || '';

                if ($btn.hasClass('loading')) return;

                $btn.addClass('loading').text('Loading...');

                $.ajax({
                    url: doomovie_ajax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'load_more_movies',
                        page: page,
                        posts_per_page: postsPerPage,
                        category: category,
                        genre: genre,
                        year: year,
                        nonce: doomovie_ajax.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            const $newItems = $(response.data.html);
                            $newItems.hide();
                            $('.movies-grid').append($newItems);
                            $newItems.fadeIn(500);

                            $btn.data('page', page);

                            if (!response.data.has_more || page >= maxPages) {
                                $btn.fadeOut();
                            }

                            DoMovie.initLazyLoading();
                        } else {
                            DoMovie.showNotification('No more movies to load', 'info');
                            $btn.fadeOut();
                        }
                    },
                    error: function() {
                        DoMovie.showNotification('Error loading movies', 'error');
                    },
                    complete: function() {
                        $btn.removeClass('loading').text('Load More');
                    }
                });
            });
        },

        /**
         * Scroll effects
         */
        initScrollEffects() {
            // Back to top button
            const $backToTop = $('<button class="back-to-top" title="Back to top"><i class="fas fa-arrow-up"></i></button>');
            $('body').append($backToTop);

            $(window).on('scroll', function() {
                if ($(window).scrollTop() > 500) {
                    $backToTop.addClass('visible');
                } else {
                    $backToTop.removeClass('visible');
                }
            });

            $backToTop.on('click', function(e) {
                e.preventDefault();
                $('html, body').animate({ scrollTop: 0 }, 500);
            });

            // Parallax effect for hero
            $(window).on('scroll', function() {
                const scrolled = $(window).scrollTop();
                const parallax = scrolled * 0.5;
                $('.hero-slide.active').css('transform', `translateY(${parallax}px)`);
            });

            // Fade in animation on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('fade-in-up');
                    }
                });
            }, observerOptions);

            $('.movie-card, .section-title').each(function() {
                observer.observe(this);
            });
        },

        /**
         * Tooltips
         */
        initTooltips() {
            $('[data-tooltip]').each(function() {
                const $element = $(this);
                const text = $element.data('tooltip');

                $element.on('mouseenter', function() {
                    const $tooltip = $(`<div class="tooltip">${text}</div>`);
                    $('body').append($tooltip);

                    const offset = $element.offset();
                    const elementWidth = $element.outerWidth();
                    const tooltipWidth = $tooltip.outerWidth();

                    $tooltip.css({
                        top: offset.top - $tooltip.outerHeight() - 10,
                        left: offset.left + (elementWidth / 2) - (tooltipWidth / 2)
                    });

                    setTimeout(() => $tooltip.addClass('visible'), 10);
                });

                $element.on('mouseleave', function() {
                    $('.tooltip').removeClass('visible');
                    setTimeout(() => $('.tooltip').remove(), 200);
                });
            });
        },

        /**
         * Lazy loading for images
         */
        initLazyLoading() {
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver(function(entries) {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            imageObserver.unobserve(img);
                        }
                    });
                });

                document.querySelectorAll('img[data-src]').forEach(img => {
                    imageObserver.observe(img);
                });
            }
        },

        /**
         * Keyboard navigation
         */
        initKeyboardNavigation() {
            $(document).on('keydown', function(e) {
                // ESC key closes modals and dropdowns
                if (e.key === 'Escape') {
                    $('.search-form-wrapper').removeClass('active');
                    $('.user-dropdown-menu').removeClass('active');
                    $('.mobile-menu').removeClass('active');
                    $('.mobile-menu-toggle').removeClass('active');
                    $('body').removeClass('mobile-menu-open').css('overflow', '');
                }

                // Ctrl/Cmd + K opens search
                if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                    e.preventDefault();
                    $('.search-toggle').click();
                }
            });
        },

        /**
         * Show notification
         */
        showNotification(message, type = 'info') {
            const $notification = $(`
                <div class="notification notification-${type}">
                    <div class="notification-content">
                        <span class="notification-message">${message}</span>
                        <button class="notification-close">&times;</button>
                    </div>
                </div>
            `);

            $('body').append($notification);

            setTimeout(() => {
                $notification.addClass('show');
            }, 100);

            // Auto hide after 4 seconds
            setTimeout(() => {
                $notification.removeClass('show');
                setTimeout(() => {
                    $notification.remove();
                }, 300);
            }, 4000);

            // Manual close
            $notification.find('.notification-close').on('click', function() {
                $notification.removeClass('show');
                setTimeout(() => {
                    $notification.remove();
                }, 300);
            });
        },

        /**
         * Track events (for analytics)
         */
        trackEvent(eventName, data = {}) {
            if (typeof gtag !== 'undefined') {
                gtag('event', eventName, data);
            }

            // Custom tracking can be added here
            console.log('Event tracked:', eventName, data);
        },

        /**
         * Initialize advanced animations
         */
        initAdvancedAnimations() {
            // Stagger animation for movie grids
            $('.movies-grid').addClass('stagger-animation');

            // Reveal on scroll
            const revealElements = document.querySelectorAll('.movie-card, .section-title, .top-10-item');
            const revealObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('revealed');
                        entry.target.style.animationDelay = Math.random() * 0.5 + 's';
                    }
                });
            }, { threshold: 0.1 });

            revealElements.forEach(el => {
                el.classList.add('reveal-on-scroll');
                revealObserver.observe(el);
            });

            // Magnetic effect for buttons
            $('.btn, .play-button, .btn-favorite, .btn-watch-later').addClass('magnetic-button');

            // Interactive shimmer effect
            $('.movie-card').addClass('interactive-element');

            // Glitch effect for hero title
            $('.hero-title').addClass('glitch-effect').attr('data-text', function() {
                return $(this).text();
            });
        },

        /**
         * Initialize particle effects
         */
        initParticleEffects() {
            // Add floating particles to hero section
            const heroSection = document.querySelector('.hero-slider-container');
            if (heroSection) {
                for (let i = 0; i < 20; i++) {
                    const particle = document.createElement('div');
                    particle.className = 'particle';
                    particle.style.cssText = `
                        position: absolute;
                        width: ${Math.random() * 4 + 2}px;
                        height: ${Math.random() * 4 + 2}px;
                        background: rgba(255, 107, 53, ${Math.random() * 0.5 + 0.2});
                        border-radius: 50%;
                        left: ${Math.random() * 100}%;
                        top: ${Math.random() * 100}%;
                        animation: float ${Math.random() * 3 + 2}s ease-in-out infinite;
                        animation-delay: ${Math.random() * 2}s;
                        pointer-events: none;
                        z-index: 1;
                    `;
                    heroSection.appendChild(particle);
                }
            }
        },

        /**
         * Initialize mouse trail effect
         */
        initMouseTrail() {
            const trail = [];
            const trailLength = 10;

            document.addEventListener('mousemove', (e) => {
                trail.push({ x: e.clientX, y: e.clientY });
                if (trail.length > trailLength) {
                    trail.shift();
                }

                // Remove existing trail elements
                document.querySelectorAll('.mouse-trail').forEach(el => el.remove());

                // Create new trail elements
                trail.forEach((point, index) => {
                    const trailElement = document.createElement('div');
                    trailElement.className = 'mouse-trail';
                    trailElement.style.cssText = `
                        position: fixed;
                        left: ${point.x}px;
                        top: ${point.y}px;
                        width: ${(index + 1) * 2}px;
                        height: ${(index + 1) * 2}px;
                        background: rgba(255, 107, 53, ${(index + 1) / trailLength * 0.5});
                        border-radius: 50%;
                        pointer-events: none;
                        z-index: 9999;
                        transform: translate(-50%, -50%);
                        transition: all 0.1s ease;
                    `;
                    document.body.appendChild(trailElement);

                    // Remove after animation
                    setTimeout(() => {
                        if (trailElement.parentNode) {
                            trailElement.remove();
                        }
                    }, 100);
                });
            });
        },

        /**
         * Initialize loading animations
         */
        initLoadingAnimations() {
            // Show skeleton screens while loading
            $('.movie-card img').each(function() {
                const $img = $(this);
                const $skeleton = $('<div class="skeleton skeleton-poster"></div>');

                $img.before($skeleton);
                $img.addClass('lazy-image');

                $img.on('load', function() {
                    $skeleton.fadeOut(300, function() {
                        $(this).remove();
                    });
                    $img.addClass('loaded');
                });
            });

            // Simulate loading for demo
            setTimeout(() => {
                $('.skeleton').fadeOut(300);
                $('.lazy-image').addClass('loaded');
            }, 1000);
        },

        /**
         * Initialize performance optimizations
         */
        initPerformanceOptimizations() {
            // Throttle scroll events
            let scrollTimeout;
            $(window).on('scroll', () => {
                if (scrollTimeout) {
                    clearTimeout(scrollTimeout);
                }
                scrollTimeout = setTimeout(() => {
                    // Scroll-based animations here
                    this.updateParallaxElements();
                }, 16); // ~60fps
            });

            // Debounce resize events
            let resizeTimeout;
            $(window).on('resize', () => {
                if (resizeTimeout) {
                    clearTimeout(resizeTimeout);
                }
                resizeTimeout = setTimeout(() => {
                    this.handleResize();
                }, 250);
            });
        },

        /**
         * Update parallax elements
         */
        updateParallaxElements() {
            const scrolled = $(window).scrollTop();
            $('.parallax-element').each(function() {
                const $element = $(this);
                const speed = $element.data('speed') || 0.5;
                const yPos = -(scrolled * speed);
                $element.css('transform', `translateY(${yPos}px)`);
            });
        },

        /**
         * Handle window resize
         */
        handleResize() {
            // Recalculate layouts if needed
            const windowWidth = $(window).width();

            if (windowWidth < 768) {
                // Mobile optimizations
                $('.mouse-trail').remove(); // Disable mouse trail on mobile
            }
        }
    };

    // Make DoMovie globally available
    window.DoMovie = DoMovie;

})(jQuery);
