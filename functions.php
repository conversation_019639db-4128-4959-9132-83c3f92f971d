<?php
/**
 * DoMovie Theme Functions
 * 
 * @package DoMovie_Theme
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define theme constants
define('DOOMOVIE_THEME_VERSION', '1.0.1');
define('DOOMOVIE_THEME_PATH', get_template_directory());
define('DOOMOVIE_THEME_URL', get_template_directory_uri());

/**
 * Theme Setup
 */
function doomovie_theme_setup() {
    // Add theme support for various features
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
        'style',
        'script'
    ));
    add_theme_support('custom-logo', array(
        'height'      => 100,
        'width'       => 300,
        'flex-height' => true,
        'flex-width'  => true,
    ));
    add_theme_support('customize-selective-refresh-widgets');
    add_theme_support('responsive-embeds');
    add_theme_support('wp-block-styles');
    add_theme_support('align-wide');

    // Add custom image sizes
    add_image_size('movie-poster', 300, 450, true);
    add_image_size('movie-backdrop', 1280, 720, true);
    add_image_size('movie-thumbnail', 150, 225, true);
    add_image_size('hero-slide', 1920, 1080, true);

    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'doomovie-theme'),
        'footer'  => __('Footer Menu', 'doomovie-theme'),
        'mobile'  => __('Mobile Menu', 'doomovie-theme'),
    ));

    // Set content width
    $GLOBALS['content_width'] = 1200;

    // Load text domain
    load_theme_textdomain('doomovie-theme', DOOMOVIE_THEME_PATH . '/languages');
}
add_action('after_setup_theme', 'doomovie_theme_setup');

/**
 * Include required files
 */
require_once DOOMOVIE_THEME_PATH . '/inc/enqueue-scripts.php';
require_once DOOMOVIE_THEME_PATH . '/inc/custom-post-types.php';
require_once DOOMOVIE_THEME_PATH . '/inc/custom-taxonomies.php';
require_once DOOMOVIE_THEME_PATH . '/inc/custom-fields.php';
require_once DOOMOVIE_THEME_PATH . '/inc/theme-functions.php';
require_once DOOMOVIE_THEME_PATH . '/inc/ajax-handlers.php';
require_once DOOMOVIE_THEME_PATH . '/inc/user-functions.php';
require_once DOOMOVIE_THEME_PATH . '/inc/admin-functions.php';
require_once DOOMOVIE_THEME_PATH . '/inc/customizer.php';
require_once DOOMOVIE_THEME_PATH . '/inc/security.php';

/**
 * Register Widget Areas
 */
function doomovie_widgets_init() {
    register_sidebar(array(
        'name'          => __('Primary Sidebar', 'doomovie-theme'),
        'id'            => 'sidebar-1',
        'description'   => __('Add widgets here.', 'doomovie-theme'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));

    register_sidebar(array(
        'name'          => __('Footer Widget Area 1', 'doomovie-theme'),
        'id'            => 'footer-1',
        'description'   => __('Add widgets here to appear in your footer.', 'doomovie-theme'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="widget-title">',
        'after_title'   => '</h4>',
    ));

    register_sidebar(array(
        'name'          => __('Footer Widget Area 2', 'doomovie-theme'),
        'id'            => 'footer-2',
        'description'   => __('Add widgets here to appear in your footer.', 'doomovie-theme'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="widget-title">',
        'after_title'   => '</h4>',
    ));

    register_sidebar(array(
        'name'          => __('Footer Widget Area 3', 'doomovie-theme'),
        'id'            => 'footer-3',
        'description'   => __('Add widgets here to appear in your footer.', 'doomovie-theme'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="widget-title">',
        'after_title'   => '</h4>',
    ));
}
add_action('widgets_init', 'doomovie_widgets_init');

/**
 * Helper Functions
 */

/**
 * Get movie poster URL
 */
function get_movie_poster($movie_id = null, $size = 'movie-poster') {
    if (!$movie_id) {
        $movie_id = get_the_ID();
    }
    
    $poster_url = get_post_meta($movie_id, 'poster_url', true);
    if ($poster_url) {
        return $poster_url;
    }
    
    $thumbnail = get_the_post_thumbnail_url($movie_id, $size);
    if ($thumbnail) {
        return $thumbnail;
    }
    
    return DOOMOVIE_THEME_URL . '/assets/images/placeholders/movie-poster.jpg';
}

/**
 * Get movie year
 */
function get_movie_year($movie_id = null) {
    if (!$movie_id) {
        $movie_id = get_the_ID();
    }
    
    $year = get_post_meta($movie_id, 'release_year', true);
    if ($year) {
        return $year;
    }
    
    $year_terms = get_the_terms($movie_id, 'movie_year');
    if ($year_terms && !is_wp_error($year_terms)) {
        return $year_terms[0]->name;
    }
    
    return get_the_date('Y', $movie_id);
}

/**
 * Get movie rating
 */
function get_movie_rating($movie_id = null) {
    if (!$movie_id) {
        $movie_id = get_the_ID();
    }
    
    $rating = get_post_meta($movie_id, 'age_rating', true);
    return $rating ? $rating : '13+';
}

/**
 * Get movie duration
 */
function get_movie_duration($movie_id = null) {
    if (!$movie_id) {
        $movie_id = get_the_ID();
    }
    
    $duration = get_post_meta($movie_id, 'duration', true);
    if ($duration) {
        $hours = floor($duration / 60);
        $minutes = $duration % 60;
        
        if ($hours > 0) {
            return sprintf('%d ชม. %d นาที', $hours, $minutes);
        } else {
            return sprintf('%d นาที', $minutes);
        }
    }
    
    return '';
}

/**
 * Get movie genres
 */
function get_movie_genres($movie_id = null) {
    if (!$movie_id) {
        $movie_id = get_the_ID();
    }
    
    $genres = get_the_terms($movie_id, 'movie_genre');
    if ($genres && !is_wp_error($genres)) {
        $genre_links = array();
        foreach ($genres as $genre) {
            $genre_links[] = sprintf(
                '<a href="%s" class="genre-tag">%s</a>',
                get_term_link($genre),
                $genre->name
            );
        }
        return implode(' ', $genre_links);
    }
    
    return '';
}

/**
 * Get movie IMDB rating
 */
function get_movie_imdb_rating($movie_id = null) {
    if (!$movie_id) {
        $movie_id = get_the_ID();
    }
    
    return get_post_meta($movie_id, 'imdb_rating', true);
}

/**
 * Check if movie is featured
 */
function is_featured_movie($movie_id = null) {
    if (!$movie_id) {
        $movie_id = get_the_ID();
    }
    
    return get_post_meta($movie_id, 'featured', true) === '1';
}

/**
 * Get movie video URLs
 */
function get_movie_video_urls($movie_id = null) {
    if (!$movie_id) {
        $movie_id = get_the_ID();
    }
    
    return array(
        'thai_1' => get_post_meta($movie_id, 'video_url_thai_1', true),
        'thai_2' => get_post_meta($movie_id, 'video_url_thai_2', true),
        'sub' => get_post_meta($movie_id, 'video_url_sub', true),
        'trailer' => get_post_meta($movie_id, 'trailer_url', true),
    );
}

/**
 * Get featured movies
 */
function get_featured_movies($limit = 8) {
    $args = array(
        'post_type' => 'movie',
        'posts_per_page' => $limit,
        'meta_query' => array(
            array(
                'key' => 'featured',
                'value' => '1',
                'compare' => '='
            )
        ),
        'orderby' => 'date',
        'order' => 'DESC'
    );
    
    return new WP_Query($args);
}

/**
 * Get latest movies
 */
function get_latest_movies($limit = 12) {
    $args = array(
        'post_type' => 'movie',
        'posts_per_page' => $limit,
        'orderby' => 'date',
        'order' => 'DESC'
    );
    
    return new WP_Query($args);
}

/**
 * Get popular movies
 */
function get_popular_movies($limit = 10) {
    $args = array(
        'post_type' => 'movie',
        'posts_per_page' => $limit,
        'meta_key' => 'view_count',
        'orderby' => 'meta_value_num',
        'order' => 'DESC'
    );
    
    return new WP_Query($args);
}

/**
 * Get related movies
 */
function get_related_movies($movie_id = null, $limit = 6) {
    if (!$movie_id) {
        $movie_id = get_the_ID();
    }
    
    $genres = get_the_terms($movie_id, 'movie_genre');
    if (!$genres || is_wp_error($genres)) {
        return get_latest_movies($limit);
    }
    
    $genre_ids = wp_list_pluck($genres, 'term_id');
    
    $args = array(
        'post_type' => 'movie',
        'posts_per_page' => $limit,
        'post__not_in' => array($movie_id),
        'tax_query' => array(
            array(
                'taxonomy' => 'movie_genre',
                'field' => 'term_id',
                'terms' => $genre_ids,
                'operator' => 'IN'
            )
        ),
        'orderby' => 'rand'
    );
    
    return new WP_Query($args);
}

/**
 * Increment movie view count
 */
function increment_movie_view_count($movie_id) {
    $current_count = get_post_meta($movie_id, 'view_count', true);
    $new_count = $current_count ? intval($current_count) + 1 : 1;
    update_post_meta($movie_id, 'view_count', $new_count);
}

// Excerpt functions moved to inc/theme-support.php

// Body classes function moved to inc/theme-support.php

// Admin bar function moved to inc/theme-functions.php

/**
 * Remove unnecessary WordPress features
 */
remove_action('wp_head', 'wp_generator');
remove_action('wp_head', 'wlwmanifest_link');
remove_action('wp_head', 'rsd_link');
remove_action('wp_head', 'wp_shortlink_wp_head');

/**
 * Security enhancements
 */
function doomovie_remove_version_strings($src) {
    if (strpos($src, 'ver=')) {
        $src = remove_query_arg('ver', $src);
    }
    return $src;
}
add_filter('style_loader_src', 'doomovie_remove_version_strings', 9999);
add_filter('script_loader_src', 'doomovie_remove_version_strings', 9999);

// Load sample data functionality
require_once DOOMOVIE_THEME_PATH . '/inc/sample-data.php';

/**
 * Enqueue scripts and styles
 */
function doomovie_scripts() {
    // Main stylesheet
    wp_enqueue_style(
        'doomovie-style',
        DOOMOVIE_THEME_URL . '/assets/css/main.css',
        array(),
        DOOMOVIE_THEME_VERSION
    );

    // Font Awesome
    wp_enqueue_style(
        'font-awesome',
        'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
        array(),
        '6.4.0'
    );

    // Google Fonts
    wp_enqueue_style(
        'google-fonts',
        'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap',
        array(),
        null
    );

    // Main JavaScript
    wp_enqueue_script(
        'doomovie-main',
        DOOMOVIE_THEME_URL . '/assets/js/main.js',
        array('jquery'),
        DOOMOVIE_THEME_VERSION,
        true
    );

    // Localize script for AJAX
    wp_localize_script('doomovie-main', 'doomovie_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('doomovie_nonce'),
        'strings' => array(
            'added_to_favorites' => __('Added to favorites', 'doomovie'),
            'removed_from_favorites' => __('Removed from favorites', 'doomovie'),
            'login_required' => __('Please login to use this feature', 'doomovie'),
        )
    ));

    // Add conditional scripts for single movie pages
    if (is_singular('movie')) {
        wp_enqueue_script(
            'doomovie-movie',
            DOOMOVIE_THEME_URL . '/assets/js/movie.js',
            array('jquery', 'doomovie-main'),
            DOOMOVIE_THEME_VERSION,
            true
        );
    }
}
add_action('wp_enqueue_scripts', 'doomovie_scripts');
