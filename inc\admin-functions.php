<?php
/**
 * Admin Functions for DoMovie Theme
 * 
 * @package DoMovie
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add admin menu pages
 */
function doomovie_add_admin_menu() {
    add_menu_page(
        __('DoMovie Settings', 'doomovie'),
        __('DoMovie', 'doomovie'),
        'manage_options',
        'doomovie-settings',
        'doomovie_admin_page',
        'dashicons-video-alt3',
        30
    );
    
    add_submenu_page(
        'doomovie-settings',
        __('General Settings', 'doomovie'),
        __('General', 'doomovie'),
        'manage_options',
        'doomovie-settings',
        'doomovie_admin_page'
    );
    
    add_submenu_page(
        'doomovie-settings',
        __('Player Settings', 'doomovie'),
        __('Player', 'doomovie'),
        'manage_options',
        'doomovie-player',
        'doomovie_player_settings_page'
    );
    
    add_submenu_page(
        'doomovie-settings',
        __('Advertisement', 'doomovie'),
        __('Ads', 'doomovie'),
        'manage_options',
        'doomovie-ads',
        'doomovie_ads_settings_page'
    );
    
    add_submenu_page(
        'doomovie-settings',
        __('Movie Requests', 'doomovie'),
        __('Requests', 'doomovie'),
        'manage_options',
        'doomovie-requests',
        'doomovie_requests_page'
    );
}
add_action('admin_menu', 'doomovie_add_admin_menu');

/**
 * Register admin settings
 */
function doomovie_admin_init() {
    // General settings
    register_setting('doomovie_general', 'doomovie_site_logo');
    register_setting('doomovie_general', 'doomovie_site_description');
    register_setting('doomovie_general', 'doomovie_contact_email');
    register_setting('doomovie_general', 'doomovie_social_facebook');
    register_setting('doomovie_general', 'doomovie_social_twitter');
    register_setting('doomovie_general', 'doomovie_social_youtube');
    register_setting('doomovie_general', 'doomovie_social_instagram');
    
    // Player settings
    register_setting('doomovie_player', 'doomovie_default_player');
    register_setting('doomovie_player', 'doomovie_player_autoplay');
    register_setting('doomovie_player', 'doomovie_player_controls');
    register_setting('doomovie_player', 'doomovie_player_quality');
    
    // Advertisement settings
    register_setting('doomovie_ads', 'doomovie_ads_header');
    register_setting('doomovie_ads', 'doomovie_ads_sidebar');
    register_setting('doomovie_ads', 'doomovie_ads_footer');
    register_setting('doomovie_ads', 'doomovie_ads_before_player');
    register_setting('doomovie_ads', 'doomovie_ads_after_player');
}
add_action('admin_init', 'doomovie_admin_init');

/**
 * Main admin page
 */
function doomovie_admin_page() {
    ?>
    <div class="wrap">
        <h1><?php _e('DoMovie General Settings', 'doomovie'); ?></h1>
        <form method="post" action="options.php">
            <?php
            settings_fields('doomovie_general');
            do_settings_sections('doomovie_general');
            ?>
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Site Logo URL', 'doomovie'); ?></th>
                    <td>
                        <input type="url" name="doomovie_site_logo" value="<?php echo esc_attr(get_option('doomovie_site_logo')); ?>" class="regular-text" />
                        <p class="description"><?php _e('Enter the URL of your site logo', 'doomovie'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Site Description', 'doomovie'); ?></th>
                    <td>
                        <textarea name="doomovie_site_description" rows="3" class="large-text"><?php echo esc_textarea(get_option('doomovie_site_description')); ?></textarea>
                        <p class="description"><?php _e('Short description of your movie site', 'doomovie'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Contact Email', 'doomovie'); ?></th>
                    <td>
                        <input type="email" name="doomovie_contact_email" value="<?php echo esc_attr(get_option('doomovie_contact_email')); ?>" class="regular-text" />
                        <p class="description"><?php _e('Email for contact and movie requests', 'doomovie'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Facebook URL', 'doomovie'); ?></th>
                    <td>
                        <input type="url" name="doomovie_social_facebook" value="<?php echo esc_attr(get_option('doomovie_social_facebook')); ?>" class="regular-text" />
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Twitter URL', 'doomovie'); ?></th>
                    <td>
                        <input type="url" name="doomovie_social_twitter" value="<?php echo esc_attr(get_option('doomovie_social_twitter')); ?>" class="regular-text" />
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('YouTube URL', 'doomovie'); ?></th>
                    <td>
                        <input type="url" name="doomovie_social_youtube" value="<?php echo esc_attr(get_option('doomovie_social_youtube')); ?>" class="regular-text" />
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Instagram URL', 'doomovie'); ?></th>
                    <td>
                        <input type="url" name="doomovie_social_instagram" value="<?php echo esc_attr(get_option('doomovie_social_instagram')); ?>" class="regular-text" />
                    </td>
                </tr>
            </table>
            <?php submit_button(); ?>
        </form>
    </div>
    <?php
}

/**
 * Player settings page
 */
function doomovie_player_settings_page() {
    ?>
    <div class="wrap">
        <h1><?php _e('Player Settings', 'doomovie'); ?></h1>
        <form method="post" action="options.php">
            <?php
            settings_fields('doomovie_player');
            do_settings_sections('doomovie_player');
            ?>
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Default Player', 'doomovie'); ?></th>
                    <td>
                        <select name="doomovie_default_player">
                            <option value="html5" <?php selected(get_option('doomovie_default_player'), 'html5'); ?>>HTML5</option>
                            <option value="jwplayer" <?php selected(get_option('doomovie_default_player'), 'jwplayer'); ?>>JW Player</option>
                            <option value="videojs" <?php selected(get_option('doomovie_default_player'), 'videojs'); ?>>Video.js</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Autoplay', 'doomovie'); ?></th>
                    <td>
                        <input type="checkbox" name="doomovie_player_autoplay" value="1" <?php checked(get_option('doomovie_player_autoplay'), 1); ?> />
                        <span class="description"><?php _e('Enable autoplay for videos', 'doomovie'); ?></span>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Show Controls', 'doomovie'); ?></th>
                    <td>
                        <input type="checkbox" name="doomovie_player_controls" value="1" <?php checked(get_option('doomovie_player_controls'), 1); ?> />
                        <span class="description"><?php _e('Show player controls', 'doomovie'); ?></span>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Default Quality', 'doomovie'); ?></th>
                    <td>
                        <select name="doomovie_player_quality">
                            <option value="auto" <?php selected(get_option('doomovie_player_quality'), 'auto'); ?>>Auto</option>
                            <option value="720p" <?php selected(get_option('doomovie_player_quality'), '720p'); ?>>720p</option>
                            <option value="1080p" <?php selected(get_option('doomovie_player_quality'), '1080p'); ?>>1080p</option>
                        </select>
                    </td>
                </tr>
            </table>
            <?php submit_button(); ?>
        </form>
    </div>
    <?php
}

/**
 * Advertisement settings page
 */
function doomovie_ads_settings_page() {
    ?>
    <div class="wrap">
        <h1><?php _e('Advertisement Settings', 'doomovie'); ?></h1>
        <form method="post" action="options.php">
            <?php
            settings_fields('doomovie_ads');
            do_settings_sections('doomovie_ads');
            ?>
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Header Ad Code', 'doomovie'); ?></th>
                    <td>
                        <textarea name="doomovie_ads_header" rows="5" class="large-text"><?php echo esc_textarea(get_option('doomovie_ads_header')); ?></textarea>
                        <p class="description"><?php _e('Ad code to display in header area', 'doomovie'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Sidebar Ad Code', 'doomovie'); ?></th>
                    <td>
                        <textarea name="doomovie_ads_sidebar" rows="5" class="large-text"><?php echo esc_textarea(get_option('doomovie_ads_sidebar')); ?></textarea>
                        <p class="description"><?php _e('Ad code to display in sidebar', 'doomovie'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Footer Ad Code', 'doomovie'); ?></th>
                    <td>
                        <textarea name="doomovie_ads_footer" rows="5" class="large-text"><?php echo esc_textarea(get_option('doomovie_ads_footer')); ?></textarea>
                        <p class="description"><?php _e('Ad code to display in footer area', 'doomovie'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Before Player Ad', 'doomovie'); ?></th>
                    <td>
                        <textarea name="doomovie_ads_before_player" rows="5" class="large-text"><?php echo esc_textarea(get_option('doomovie_ads_before_player')); ?></textarea>
                        <p class="description"><?php _e('Ad code to display before video player', 'doomovie'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('After Player Ad', 'doomovie'); ?></th>
                    <td>
                        <textarea name="doomovie_ads_after_player" rows="5" class="large-text"><?php echo esc_textarea(get_option('doomovie_ads_after_player')); ?></textarea>
                        <p class="description"><?php _e('Ad code to display after video player', 'doomovie'); ?></p>
                    </td>
                </tr>
            </table>
            <?php submit_button(); ?>
        </form>
    </div>
    <?php
}

/**
 * Movie requests page
 */
function doomovie_requests_page() {
    ?>
    <div class="wrap">
        <h1><?php _e('Movie Requests', 'doomovie'); ?></h1>
        <?php
        // Get movie requests
        $requests = get_posts(array(
            'post_type' => 'movie_request',
            'post_status' => array('pending', 'publish'),
            'posts_per_page' => 50,
            'orderby' => 'date',
            'order' => 'DESC'
        ));
        
        if ($requests) {
            echo '<table class="wp-list-table widefat fixed striped">';
            echo '<thead><tr><th>Movie Title</th><th>Year</th><th>Requester</th><th>Date</th><th>Status</th><th>Actions</th></tr></thead>';
            echo '<tbody>';
            
            foreach ($requests as $request) {
                $movie_title = get_post_meta($request->ID, '_requested_movie_title', true);
                $movie_year = get_post_meta($request->ID, '_requested_movie_year', true);
                $requester_name = get_post_meta($request->ID, '_requester_name', true);
                $requester_email = get_post_meta($request->ID, '_requester_email', true);
                
                echo '<tr>';
                echo '<td><strong>' . esc_html($movie_title) . '</strong></td>';
                echo '<td>' . esc_html($movie_year) . '</td>';
                echo '<td>' . esc_html($requester_name) . '<br><small>' . esc_html($requester_email) . '</small></td>';
                echo '<td>' . get_the_date('Y-m-d H:i', $request->ID) . '</td>';
                echo '<td>' . ucfirst($request->post_status) . '</td>';
                echo '<td>';
                echo '<a href="' . get_edit_post_link($request->ID) . '" class="button button-small">Edit</a> ';
                echo '<a href="' . get_delete_post_link($request->ID) . '" class="button button-small">Delete</a>';
                echo '</td>';
                echo '</tr>';
            }
            
            echo '</tbody></table>';
        } else {
            echo '<p>' . __('No movie requests found.', 'doomovie') . '</p>';
        }
        ?>
    </div>
    <?php
}

/**
 * Add custom columns to movie post type
 */
function doomovie_add_movie_columns($columns) {
    $new_columns = array();
    $new_columns['cb'] = $columns['cb'];
    $new_columns['title'] = $columns['title'];
    $new_columns['poster'] = __('Poster', 'doomovie');
    $new_columns['year'] = __('Year', 'doomovie');
    $new_columns['rating'] = __('Rating', 'doomovie');
    $new_columns['quality'] = __('Quality', 'doomovie');
    $new_columns['genres'] = __('Genres', 'doomovie');
    $new_columns['date'] = $columns['date'];
    
    return $new_columns;
}
add_filter('manage_movie_posts_columns', 'doomovie_add_movie_columns');

/**
 * Display custom column content
 */
function doomovie_movie_column_content($column, $post_id) {
    switch ($column) {
        case 'poster':
            if (has_post_thumbnail($post_id)) {
                echo get_the_post_thumbnail($post_id, array(50, 75));
            } else {
                echo '—';
            }
            break;
            
        case 'year':
            $year = doomovie_get_movie_meta($post_id, 'year');
            echo $year ? $year : '—';
            break;
            
        case 'rating':
            $rating = doomovie_get_movie_meta($post_id, 'rating');
            echo $rating ? $rating : '—';
            break;
            
        case 'quality':
            $quality = doomovie_get_movie_meta($post_id, 'quality');
            echo $quality ? $quality : '—';
            break;
            
        case 'genres':
            $terms = get_the_terms($post_id, 'movie_genre');
            if ($terms && !is_wp_error($terms)) {
                $genre_names = array();
                foreach ($terms as $term) {
                    $genre_names[] = $term->name;
                }
                echo implode(', ', $genre_names);
            } else {
                echo '—';
            }
            break;
    }
}
add_action('manage_movie_posts_custom_column', 'doomovie_movie_column_content', 10, 2);
