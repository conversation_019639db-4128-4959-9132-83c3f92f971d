<?php
/**
 * AJAX Handlers for DoMovie Theme
 * 
 * @package DoMovie
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Load more movies via AJAX
 */
function doomovie_load_more_movies() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'doomovie_ajax_nonce')) {
        wp_die('Security check failed');
    }
    
    $page = intval($_POST['page']);
    $posts_per_page = intval($_POST['posts_per_page']) ?: 12;
    $category = sanitize_text_field($_POST['category']);
    $genre = sanitize_text_field($_POST['genre']);
    $year = sanitize_text_field($_POST['year']);
    
    $args = array(
        'post_type' => 'movie',
        'posts_per_page' => $posts_per_page,
        'paged' => $page,
        'post_status' => 'publish'
    );
    
    // Add taxonomy queries
    $tax_query = array();
    
    if (!empty($genre)) {
        $tax_query[] = array(
            'taxonomy' => 'movie_genre',
            'field'    => 'slug',
            'terms'    => $genre,
        );
    }
    
    if (!empty($year)) {
        $tax_query[] = array(
            'taxonomy' => 'movie_year',
            'field'    => 'slug',
            'terms'    => $year,
        );
    }
    
    if (!empty($tax_query)) {
        $args['tax_query'] = $tax_query;
    }
    
    $query = new WP_Query($args);
    
    if ($query->have_posts()) {
        ob_start();
        while ($query->have_posts()) {
            $query->the_post();
            get_template_part('template-parts/movie/movie-card');
        }
        $html = ob_get_clean();
        
        wp_send_json_success(array(
            'html' => $html,
            'has_more' => $page < $query->max_num_pages
        ));
    } else {
        wp_send_json_error('No more movies found');
    }
    
    wp_reset_postdata();
    wp_die();
}
add_action('wp_ajax_load_more_movies', 'doomovie_load_more_movies');
add_action('wp_ajax_nopriv_load_more_movies', 'doomovie_load_more_movies');

/**
 * Search movies via AJAX
 */
function doomovie_search_movies() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'doomovie_ajax_nonce')) {
        wp_die('Security check failed');
    }
    
    $search_term = sanitize_text_field($_POST['search_term']);
    $limit = intval($_POST['limit']) ?: 10;
    
    if (empty($search_term)) {
        wp_send_json_error('Search term is required');
    }
    
    $args = array(
        'post_type' => 'movie',
        'posts_per_page' => $limit,
        's' => $search_term,
        'post_status' => 'publish'
    );
    
    $query = new WP_Query($args);
    $results = array();
    
    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            $results[] = array(
                'id' => get_the_ID(),
                'title' => get_the_title(),
                'url' => get_permalink(),
                'poster' => get_the_post_thumbnail_url(get_the_ID(), 'movie-thumb-small'),
                'year' => doomovie_get_movie_meta(get_the_ID(), 'year'),
                'rating' => doomovie_get_movie_meta(get_the_ID(), 'rating'),
                'quality' => doomovie_get_movie_meta(get_the_ID(), 'quality')
            );
        }
    }
    
    wp_reset_postdata();
    wp_send_json_success($results);
    wp_die();
}
add_action('wp_ajax_search_movies', 'doomovie_search_movies');
add_action('wp_ajax_nopriv_search_movies', 'doomovie_search_movies');

/**
 * Add movie to favorites
 */
function doomovie_add_to_favorites() {
    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error('You must be logged in to add favorites');
    }
    
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'doomovie_ajax_nonce')) {
        wp_die('Security check failed');
    }
    
    $movie_id = intval($_POST['movie_id']);
    $user_id = get_current_user_id();
    
    if (!$movie_id) {
        wp_send_json_error('Invalid movie ID');
    }
    
    // Get current favorites
    $favorites = get_user_meta($user_id, 'favorite_movies', true);
    if (!is_array($favorites)) {
        $favorites = array();
    }
    
    // Check if already in favorites
    if (in_array($movie_id, $favorites)) {
        wp_send_json_error('Movie already in favorites');
    }
    
    // Add to favorites
    $favorites[] = $movie_id;
    update_user_meta($user_id, 'favorite_movies', $favorites);
    
    wp_send_json_success('Movie added to favorites');
    wp_die();
}
add_action('wp_ajax_add_to_favorites', 'doomovie_add_to_favorites');

/**
 * Remove movie from favorites
 */
function doomovie_remove_from_favorites() {
    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error('You must be logged in to remove favorites');
    }
    
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'doomovie_ajax_nonce')) {
        wp_die('Security check failed');
    }
    
    $movie_id = intval($_POST['movie_id']);
    $user_id = get_current_user_id();
    
    if (!$movie_id) {
        wp_send_json_error('Invalid movie ID');
    }
    
    // Get current favorites
    $favorites = get_user_meta($user_id, 'favorite_movies', true);
    if (!is_array($favorites)) {
        $favorites = array();
    }
    
    // Remove from favorites
    $key = array_search($movie_id, $favorites);
    if ($key !== false) {
        unset($favorites[$key]);
        update_user_meta($user_id, 'favorite_movies', array_values($favorites));
        wp_send_json_success('Movie removed from favorites');
    } else {
        wp_send_json_error('Movie not in favorites');
    }
    
    wp_die();
}
add_action('wp_ajax_remove_from_favorites', 'doomovie_remove_from_favorites');

/**
 * Rate movie
 */
function doomovie_rate_movie() {
    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error('You must be logged in to rate movies');
    }
    
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'doomovie_ajax_nonce')) {
        wp_die('Security check failed');
    }
    
    $movie_id = intval($_POST['movie_id']);
    $rating = floatval($_POST['rating']);
    $user_id = get_current_user_id();
    
    if (!$movie_id || $rating < 1 || $rating > 10) {
        wp_send_json_error('Invalid movie ID or rating');
    }
    
    // Save user rating
    $user_ratings = get_user_meta($user_id, 'movie_ratings', true);
    if (!is_array($user_ratings)) {
        $user_ratings = array();
    }
    $user_ratings[$movie_id] = $rating;
    update_user_meta($user_id, 'movie_ratings', $user_ratings);
    
    // Update movie average rating
    $all_ratings = get_post_meta($movie_id, '_user_ratings', true);
    if (!is_array($all_ratings)) {
        $all_ratings = array();
    }
    $all_ratings[$user_id] = $rating;
    update_post_meta($movie_id, '_user_ratings', $all_ratings);
    
    // Calculate average
    $average = array_sum($all_ratings) / count($all_ratings);
    update_post_meta($movie_id, '_average_rating', $average);
    update_post_meta($movie_id, '_rating_count', count($all_ratings));
    
    wp_send_json_success(array(
        'average' => round($average, 1),
        'count' => count($all_ratings)
    ));
    wp_die();
}
add_action('wp_ajax_rate_movie', 'doomovie_rate_movie');

/**
 * Request movie
 */
function doomovie_request_movie() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'doomovie_ajax_nonce')) {
        wp_die('Security check failed');
    }
    
    $movie_title = sanitize_text_field($_POST['movie_title']);
    $movie_year = sanitize_text_field($_POST['movie_year']);
    $user_email = sanitize_email($_POST['user_email']);
    $user_name = sanitize_text_field($_POST['user_name']);
    $message = sanitize_textarea_field($_POST['message']);
    
    if (empty($movie_title)) {
        wp_send_json_error('Movie title is required');
    }
    
    // Create movie request post
    $post_data = array(
        'post_title' => 'Movie Request: ' . $movie_title,
        'post_content' => $message,
        'post_status' => 'pending',
        'post_type' => 'movie_request',
        'meta_input' => array(
            '_requested_movie_title' => $movie_title,
            '_requested_movie_year' => $movie_year,
            '_requester_email' => $user_email,
            '_requester_name' => $user_name,
            '_request_date' => current_time('mysql')
        )
    );
    
    $post_id = wp_insert_post($post_data);
    
    if ($post_id) {
        wp_send_json_success('Movie request submitted successfully');
    } else {
        wp_send_json_error('Failed to submit movie request');
    }
    
    wp_die();
}
add_action('wp_ajax_request_movie', 'doomovie_request_movie');
add_action('wp_ajax_nopriv_request_movie', 'doomovie_request_movie');
