<?php
/**
 * Custom Fields for DoMovie Theme
 * 
 * @package DoMovie
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add custom meta boxes for movie post type
 */
function doomovie_add_movie_meta_boxes() {
    add_meta_box(
        'movie_details',
        __('Movie Details', 'doomovie'),
        'doomovie_movie_details_callback',
        'movie',
        'normal',
        'high'
    );
    
    add_meta_box(
        'movie_media',
        __('Movie Media', 'doomovie'),
        'doomovie_movie_media_callback',
        'movie',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'doomovie_add_movie_meta_boxes');

/**
 * Movie details meta box callback
 */
function doomovie_movie_details_callback($post) {
    wp_nonce_field('doomovie_movie_details_nonce', 'movie_details_nonce');
    
    $year = get_post_meta($post->ID, '_movie_year', true);
    $duration = get_post_meta($post->ID, '_movie_duration', true);
    $rating = get_post_meta($post->ID, '_movie_rating', true);
    $quality = get_post_meta($post->ID, '_movie_quality', true);
    $imdb_rating = get_post_meta($post->ID, '_movie_imdb_rating', true);
    $director = get_post_meta($post->ID, '_movie_director', true);
    $cast = get_post_meta($post->ID, '_movie_cast', true);
    $country = get_post_meta($post->ID, '_movie_country', true);
    $language = get_post_meta($post->ID, '_movie_language', true);
    ?>
    <table class="form-table">
        <tr>
            <th><label for="movie_year"><?php _e('Release Year', 'doomovie'); ?></label></th>
            <td><input type="number" id="movie_year" name="movie_year" value="<?php echo esc_attr($year); ?>" min="1900" max="2030" /></td>
        </tr>
        <tr>
            <th><label for="movie_duration"><?php _e('Duration (minutes)', 'doomovie'); ?></label></th>
            <td><input type="number" id="movie_duration" name="movie_duration" value="<?php echo esc_attr($duration); ?>" min="1" /></td>
        </tr>
        <tr>
            <th><label for="movie_rating"><?php _e('Age Rating', 'doomovie'); ?></label></th>
            <td>
                <select id="movie_rating" name="movie_rating">
                    <option value=""><?php _e('Select Rating', 'doomovie'); ?></option>
                    <option value="G" <?php selected($rating, 'G'); ?>>G</option>
                    <option value="PG" <?php selected($rating, 'PG'); ?>>PG</option>
                    <option value="PG-13" <?php selected($rating, 'PG-13'); ?>>PG-13</option>
                    <option value="R" <?php selected($rating, 'R'); ?>>R</option>
                    <option value="NC-17" <?php selected($rating, 'NC-17'); ?>>NC-17</option>
                    <option value="7+" <?php selected($rating, '7+'); ?>>7+</option>
                    <option value="13+" <?php selected($rating, '13+'); ?>>13+</option>
                    <option value="15+" <?php selected($rating, '15+'); ?>>15+</option>
                    <option value="18+" <?php selected($rating, '18+'); ?>>18+</option>
                </select>
            </td>
        </tr>
        <tr>
            <th><label for="movie_quality"><?php _e('Video Quality', 'doomovie'); ?></label></th>
            <td>
                <select id="movie_quality" name="movie_quality">
                    <option value=""><?php _e('Select Quality', 'doomovie'); ?></option>
                    <option value="HD" <?php selected($quality, 'HD'); ?>>HD</option>
                    <option value="Full HD" <?php selected($quality, 'Full HD'); ?>>Full HD</option>
                    <option value="4K" <?php selected($quality, '4K'); ?>>4K</option>
                    <option value="CAM" <?php selected($quality, 'CAM'); ?>>CAM</option>
                    <option value="TS" <?php selected($quality, 'TS'); ?>>TS</option>
                </select>
            </td>
        </tr>
        <tr>
            <th><label for="movie_imdb_rating"><?php _e('IMDB Rating', 'doomovie'); ?></label></th>
            <td><input type="number" id="movie_imdb_rating" name="movie_imdb_rating" value="<?php echo esc_attr($imdb_rating); ?>" min="0" max="10" step="0.1" /></td>
        </tr>
        <tr>
            <th><label for="movie_director"><?php _e('Director', 'doomovie'); ?></label></th>
            <td><input type="text" id="movie_director" name="movie_director" value="<?php echo esc_attr($director); ?>" class="regular-text" /></td>
        </tr>
        <tr>
            <th><label for="movie_cast"><?php _e('Cast', 'doomovie'); ?></label></th>
            <td><textarea id="movie_cast" name="movie_cast" rows="3" class="large-text"><?php echo esc_textarea($cast); ?></textarea></td>
        </tr>
        <tr>
            <th><label for="movie_country"><?php _e('Country', 'doomovie'); ?></label></th>
            <td><input type="text" id="movie_country" name="movie_country" value="<?php echo esc_attr($country); ?>" class="regular-text" /></td>
        </tr>
        <tr>
            <th><label for="movie_language"><?php _e('Language', 'doomovie'); ?></label></th>
            <td><input type="text" id="movie_language" name="movie_language" value="<?php echo esc_attr($language); ?>" class="regular-text" /></td>
        </tr>
    </table>
    <?php
}

/**
 * Movie media meta box callback
 */
function doomovie_movie_media_callback($post) {
    wp_nonce_field('doomovie_movie_media_nonce', 'movie_media_nonce');
    
    $trailer_url = get_post_meta($post->ID, '_movie_trailer_url', true);
    $movie_url = get_post_meta($post->ID, '_movie_url', true);
    $download_links = get_post_meta($post->ID, '_movie_download_links', true);
    $poster_url = get_post_meta($post->ID, '_movie_poster_url', true);
    ?>
    <table class="form-table">
        <tr>
            <th><label for="movie_trailer_url"><?php _e('Trailer URL', 'doomovie'); ?></label></th>
            <td><input type="url" id="movie_trailer_url" name="movie_trailer_url" value="<?php echo esc_url($trailer_url); ?>" class="large-text" /></td>
        </tr>
        <tr>
            <th><label for="movie_url"><?php _e('Movie Stream URL', 'doomovie'); ?></label></th>
            <td><input type="url" id="movie_url" name="movie_url" value="<?php echo esc_url($movie_url); ?>" class="large-text" /></td>
        </tr>
        <tr>
            <th><label for="movie_download_links"><?php _e('Download Links (JSON)', 'doomovie'); ?></label></th>
            <td><textarea id="movie_download_links" name="movie_download_links" rows="5" class="large-text" placeholder='[{"quality":"HD","url":"http://example.com/download1"},{"quality":"4K","url":"http://example.com/download2"}]'><?php echo esc_textarea($download_links); ?></textarea></td>
        </tr>
        <tr>
            <th><label for="movie_poster_url"><?php _e('Poster URL', 'doomovie'); ?></label></th>
            <td><input type="url" id="movie_poster_url" name="movie_poster_url" value="<?php echo esc_url($poster_url); ?>" class="large-text" /></td>
        </tr>
    </table>
    <?php
}

/**
 * Save movie meta data
 */
function doomovie_save_movie_meta($post_id) {
    // Check if nonce is valid
    if (!isset($_POST['movie_details_nonce']) || !wp_verify_nonce($_POST['movie_details_nonce'], 'doomovie_movie_details_nonce')) {
        return;
    }
    
    if (!isset($_POST['movie_media_nonce']) || !wp_verify_nonce($_POST['movie_media_nonce'], 'doomovie_movie_media_nonce')) {
        return;
    }
    
    // Check if user has permission to edit post
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    
    // Save movie details
    $fields = array(
        'movie_year' => 'intval',
        'movie_duration' => 'intval', 
        'movie_rating' => 'sanitize_text_field',
        'movie_quality' => 'sanitize_text_field',
        'movie_imdb_rating' => 'floatval',
        'movie_director' => 'sanitize_text_field',
        'movie_cast' => 'sanitize_textarea_field',
        'movie_country' => 'sanitize_text_field',
        'movie_language' => 'sanitize_text_field',
        'movie_trailer_url' => 'esc_url_raw',
        'movie_url' => 'esc_url_raw',
        'movie_download_links' => 'sanitize_textarea_field',
        'movie_poster_url' => 'esc_url_raw'
    );
    
    foreach ($fields as $field => $sanitize_function) {
        if (isset($_POST[$field])) {
            $value = call_user_func($sanitize_function, $_POST[$field]);
            update_post_meta($post_id, '_' . $field, $value);
        }
    }
}
add_action('save_post', 'doomovie_save_movie_meta');

/**
 * Helper function to get movie meta
 */
function doomovie_get_movie_meta($post_id, $key, $default = '') {
    $value = get_post_meta($post_id, '_movie_' . $key, true);
    return !empty($value) ? $value : $default;
}
