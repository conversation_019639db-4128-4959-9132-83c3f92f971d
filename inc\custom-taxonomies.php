<?php
/**
 * Custom Taxonomies for DoMovie Theme
 * 
 * @package DoMovie_Theme
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Register Movie Genre Taxonomy
 */
function doomovie_register_movie_genre_taxonomy() {
    $labels = array(
        'name'                       => _x('Movie Genres', 'Taxonomy General Name', 'doomovie-theme'),
        'singular_name'              => _x('Movie Genre', 'Taxonomy Singular Name', 'doomovie-theme'),
        'menu_name'                  => __('Genres', 'doomovie-theme'),
        'all_items'                  => __('All Genres', 'doomovie-theme'),
        'parent_item'                => __('Parent Genre', 'doomovie-theme'),
        'parent_item_colon'          => __('Parent Genre:', 'doomovie-theme'),
        'new_item_name'              => __('New Genre Name', 'doomovie-theme'),
        'add_new_item'               => __('Add New Genre', 'doomovie-theme'),
        'edit_item'                  => __('Edit Genre', 'doomovie-theme'),
        'update_item'                => __('Update Genre', 'doomovie-theme'),
        'view_item'                  => __('View Genre', 'doomovie-theme'),
        'separate_items_with_commas' => __('Separate genres with commas', 'doomovie-theme'),
        'add_or_remove_items'        => __('Add or remove genres', 'doomovie-theme'),
        'choose_from_most_used'      => __('Choose from the most used', 'doomovie-theme'),
        'popular_items'              => __('Popular Genres', 'doomovie-theme'),
        'search_items'               => __('Search Genres', 'doomovie-theme'),
        'not_found'                  => __('Not Found', 'doomovie-theme'),
        'no_terms'                   => __('No genres', 'doomovie-theme'),
        'items_list'                 => __('Genres list', 'doomovie-theme'),
        'items_list_navigation'      => __('Genres list navigation', 'doomovie-theme'),
    );

    $args = array(
        'labels'                     => $labels,
        'hierarchical'               => true,
        'public'                     => true,
        'show_ui'                    => true,
        'show_admin_column'          => true,
        'show_in_nav_menus'          => true,
        'show_tagcloud'              => true,
        'show_in_rest'               => true,
        'rewrite'                    => array('slug' => 'movies'),
    );

    register_taxonomy('movie_genre', array('movie'), $args);
}
add_action('init', 'doomovie_register_movie_genre_taxonomy', 0);

/**
 * Register Movie Country Taxonomy
 */
function doomovie_register_movie_country_taxonomy() {
    $labels = array(
        'name'                       => _x('Countries', 'Taxonomy General Name', 'doomovie-theme'),
        'singular_name'              => _x('Country', 'Taxonomy Singular Name', 'doomovie-theme'),
        'menu_name'                  => __('Countries', 'doomovie-theme'),
        'all_items'                  => __('All Countries', 'doomovie-theme'),
        'parent_item'                => __('Parent Country', 'doomovie-theme'),
        'parent_item_colon'          => __('Parent Country:', 'doomovie-theme'),
        'new_item_name'              => __('New Country Name', 'doomovie-theme'),
        'add_new_item'               => __('Add New Country', 'doomovie-theme'),
        'edit_item'                  => __('Edit Country', 'doomovie-theme'),
        'update_item'                => __('Update Country', 'doomovie-theme'),
        'view_item'                  => __('View Country', 'doomovie-theme'),
        'separate_items_with_commas' => __('Separate countries with commas', 'doomovie-theme'),
        'add_or_remove_items'        => __('Add or remove countries', 'doomovie-theme'),
        'choose_from_most_used'      => __('Choose from the most used', 'doomovie-theme'),
        'popular_items'              => __('Popular Countries', 'doomovie-theme'),
        'search_items'               => __('Search Countries', 'doomovie-theme'),
        'not_found'                  => __('Not Found', 'doomovie-theme'),
        'no_terms'                   => __('No countries', 'doomovie-theme'),
        'items_list'                 => __('Countries list', 'doomovie-theme'),
        'items_list_navigation'      => __('Countries list navigation', 'doomovie-theme'),
    );

    $args = array(
        'labels'                     => $labels,
        'hierarchical'               => true,
        'public'                     => true,
        'show_ui'                    => true,
        'show_admin_column'          => true,
        'show_in_nav_menus'          => true,
        'show_tagcloud'              => true,
        'show_in_rest'               => true,
        'rewrite'                    => array('slug' => 'country'),
    );

    register_taxonomy('movie_country', array('movie'), $args);
}
add_action('init', 'doomovie_register_movie_country_taxonomy', 0);

/**
 * Register Movie Year Taxonomy
 */
function doomovie_register_movie_year_taxonomy() {
    $labels = array(
        'name'                       => _x('Years', 'Taxonomy General Name', 'doomovie-theme'),
        'singular_name'              => _x('Year', 'Taxonomy Singular Name', 'doomovie-theme'),
        'menu_name'                  => __('Years', 'doomovie-theme'),
        'all_items'                  => __('All Years', 'doomovie-theme'),
        'new_item_name'              => __('New Year Name', 'doomovie-theme'),
        'add_new_item'               => __('Add New Year', 'doomovie-theme'),
        'edit_item'                  => __('Edit Year', 'doomovie-theme'),
        'update_item'                => __('Update Year', 'doomovie-theme'),
        'view_item'                  => __('View Year', 'doomovie-theme'),
        'separate_items_with_commas' => __('Separate years with commas', 'doomovie-theme'),
        'add_or_remove_items'        => __('Add or remove years', 'doomovie-theme'),
        'choose_from_most_used'      => __('Choose from the most used', 'doomovie-theme'),
        'popular_items'              => __('Popular Years', 'doomovie-theme'),
        'search_items'               => __('Search Years', 'doomovie-theme'),
        'not_found'                  => __('Not Found', 'doomovie-theme'),
        'no_terms'                   => __('No years', 'doomovie-theme'),
        'items_list'                 => __('Years list', 'doomovie-theme'),
        'items_list_navigation'      => __('Years list navigation', 'doomovie-theme'),
    );

    $args = array(
        'labels'                     => $labels,
        'hierarchical'               => false,
        'public'                     => true,
        'show_ui'                    => true,
        'show_admin_column'          => true,
        'show_in_nav_menus'          => true,
        'show_tagcloud'              => true,
        'show_in_rest'               => true,
        'rewrite'                    => array('slug' => 'year'),
    );

    register_taxonomy('movie_year', array('movie'), $args);
}
add_action('init', 'doomovie_register_movie_year_taxonomy', 0);

/**
 * Register Movie Quality Taxonomy
 */
function doomovie_register_movie_quality_taxonomy() {
    $labels = array(
        'name'                       => _x('Quality', 'Taxonomy General Name', 'doomovie-theme'),
        'singular_name'              => _x('Quality', 'Taxonomy Singular Name', 'doomovie-theme'),
        'menu_name'                  => __('Quality', 'doomovie-theme'),
        'all_items'                  => __('All Quality', 'doomovie-theme'),
        'new_item_name'              => __('New Quality Name', 'doomovie-theme'),
        'add_new_item'               => __('Add New Quality', 'doomovie-theme'),
        'edit_item'                  => __('Edit Quality', 'doomovie-theme'),
        'update_item'                => __('Update Quality', 'doomovie-theme'),
        'view_item'                  => __('View Quality', 'doomovie-theme'),
        'separate_items_with_commas' => __('Separate quality with commas', 'doomovie-theme'),
        'add_or_remove_items'        => __('Add or remove quality', 'doomovie-theme'),
        'choose_from_most_used'      => __('Choose from the most used', 'doomovie-theme'),
        'popular_items'              => __('Popular Quality', 'doomovie-theme'),
        'search_items'               => __('Search Quality', 'doomovie-theme'),
        'not_found'                  => __('Not Found', 'doomovie-theme'),
        'no_terms'                   => __('No quality', 'doomovie-theme'),
        'items_list'                 => __('Quality list', 'doomovie-theme'),
        'items_list_navigation'      => __('Quality list navigation', 'doomovie-theme'),
    );

    $args = array(
        'labels'                     => $labels,
        'hierarchical'               => false,
        'public'                     => true,
        'show_ui'                    => true,
        'show_admin_column'          => true,
        'show_in_nav_menus'          => true,
        'show_tagcloud'              => false,
        'show_in_rest'               => true,
        'rewrite'                    => array('slug' => 'quality'),
    );

    register_taxonomy('movie_quality', array('movie'), $args);
}
add_action('init', 'doomovie_register_movie_quality_taxonomy', 0);

/**
 * Register Movie Language Taxonomy
 */
function doomovie_register_movie_language_taxonomy() {
    $labels = array(
        'name'                       => _x('Languages', 'Taxonomy General Name', 'doomovie-theme'),
        'singular_name'              => _x('Language', 'Taxonomy Singular Name', 'doomovie-theme'),
        'menu_name'                  => __('Languages', 'doomovie-theme'),
        'all_items'                  => __('All Languages', 'doomovie-theme'),
        'new_item_name'              => __('New Language Name', 'doomovie-theme'),
        'add_new_item'               => __('Add New Language', 'doomovie-theme'),
        'edit_item'                  => __('Edit Language', 'doomovie-theme'),
        'update_item'                => __('Update Language', 'doomovie-theme'),
        'view_item'                  => __('View Language', 'doomovie-theme'),
        'separate_items_with_commas' => __('Separate languages with commas', 'doomovie-theme'),
        'add_or_remove_items'        => __('Add or remove languages', 'doomovie-theme'),
        'choose_from_most_used'      => __('Choose from the most used', 'doomovie-theme'),
        'popular_items'              => __('Popular Languages', 'doomovie-theme'),
        'search_items'               => __('Search Languages', 'doomovie-theme'),
        'not_found'                  => __('Not Found', 'doomovie-theme'),
        'no_terms'                   => __('No languages', 'doomovie-theme'),
        'items_list'                 => __('Languages list', 'doomovie-theme'),
        'items_list_navigation'      => __('Languages list navigation', 'doomovie-theme'),
    );

    $args = array(
        'labels'                     => $labels,
        'hierarchical'               => false,
        'public'                     => true,
        'show_ui'                    => true,
        'show_admin_column'          => true,
        'show_in_nav_menus'          => true,
        'show_tagcloud'              => true,
        'show_in_rest'               => true,
        'rewrite'                    => array('slug' => 'language'),
    );

    register_taxonomy('movie_language', array('movie'), $args);
}
add_action('init', 'doomovie_register_movie_language_taxonomy', 0);

/**
 * Create default terms for taxonomies
 */
function doomovie_create_default_terms() {
    // Movie Genres
    $genres = array(
        'หนังแอคชั่นบู๊' => 'Action',
        'หนังดราม่า' => 'Drama',
        'หนังตลก' => 'Comedy',
        'หนังผจญภัย' => 'Adventure',
        'หนังระทึกขวัญ' => 'Thriller',
        'หนังวิทยาศาสตร์' => 'Sci-Fi',
        'หนังแฟนตาซี' => 'Fantasy',
        'หนังสยองขวัญ' => 'Horror',
        'หนังซอมบี้' => 'Zombie',
        'หนังรัก' => 'Romance',
        'หนังสงคราม' => 'War',
        'หนังอาชญากรรม' => 'Crime',
        'หนังสารคดี' => 'Documentary',
        'หนังเด็ก' => 'Family',
        'หนังดนตรี' => 'Musical'
    );

    foreach ($genres as $thai_name => $english_name) {
        if (!term_exists($thai_name, 'movie_genre')) {
            wp_insert_term($thai_name, 'movie_genre', array(
                'description' => $english_name,
                'slug' => sanitize_title($thai_name)
            ));
        }
    }

    // Movie Countries
    $countries = array(
        'หนังไทย' => 'Thailand',
        'หนังฝรั่ง' => 'Western',
        'หนังเกาหลี' => 'Korea',
        'หนังญี่ปุ่น' => 'Japan',
        'หนังจีน' => 'China',
        'หนังอินเดีย' => 'India',
        'หนังเอเชีย' => 'Asia'
    );

    foreach ($countries as $thai_name => $english_name) {
        if (!term_exists($thai_name, 'movie_country')) {
            wp_insert_term($thai_name, 'movie_country', array(
                'description' => $english_name,
                'slug' => sanitize_title($thai_name)
            ));
        }
    }

    // Movie Quality
    $qualities = array('HD', '4K', 'Full HD', 'CAM', 'TS');
    foreach ($qualities as $quality) {
        if (!term_exists($quality, 'movie_quality')) {
            wp_insert_term($quality, 'movie_quality');
        }
    }

    // Movie Languages
    $languages = array('พากย์ไทย', 'ซับไทย', 'พากย์อังกฤษ', 'ซับอังกฤษ');
    foreach ($languages as $language) {
        if (!term_exists($language, 'movie_language')) {
            wp_insert_term($language, 'movie_language');
        }
    }

    // Movie Years (last 10 years + current year + next 2 years)
    $current_year = date('Y');
    for ($year = $current_year - 10; $year <= $current_year + 2; $year++) {
        if (!term_exists($year, 'movie_year')) {
            wp_insert_term($year, 'movie_year');
        }
    }
}
add_action('after_switch_theme', 'doomovie_create_default_terms');

/**
 * Add custom fields to taxonomy terms
 */
function doomovie_add_taxonomy_custom_fields($taxonomy) {
    ?>
    <div class="form-field">
        <label for="term_color"><?php _e('Color', 'doomovie-theme'); ?></label>
        <input type="color" name="term_color" id="term_color" value="#e50914">
        <p class="description"><?php _e('Choose a color for this term.', 'doomovie-theme'); ?></p>
    </div>
    
    <div class="form-field">
        <label for="term_icon"><?php _e('Icon', 'doomovie-theme'); ?></label>
        <input type="text" name="term_icon" id="term_icon" value="">
        <p class="description"><?php _e('Enter an icon class (e.g., dashicons-video-alt3).', 'doomovie-theme'); ?></p>
    </div>
    
    <div class="form-field">
        <label for="term_featured"><?php _e('Featured', 'doomovie-theme'); ?></label>
        <input type="checkbox" name="term_featured" id="term_featured" value="1">
        <p class="description"><?php _e('Mark this term as featured.', 'doomovie-theme'); ?></p>
    </div>
    <?php
}
add_action('movie_genre_add_form_fields', 'doomovie_add_taxonomy_custom_fields');
add_action('movie_country_add_form_fields', 'doomovie_add_taxonomy_custom_fields');

/**
 * Save custom taxonomy fields
 */
function doomovie_save_taxonomy_custom_fields($term_id) {
    if (isset($_POST['term_color'])) {
        update_term_meta($term_id, 'term_color', sanitize_hex_color($_POST['term_color']));
    }
    
    if (isset($_POST['term_icon'])) {
        update_term_meta($term_id, 'term_icon', sanitize_text_field($_POST['term_icon']));
    }
    
    if (isset($_POST['term_featured'])) {
        update_term_meta($term_id, 'term_featured', '1');
    } else {
        update_term_meta($term_id, 'term_featured', '0');
    }
}
add_action('created_movie_genre', 'doomovie_save_taxonomy_custom_fields');
add_action('created_movie_country', 'doomovie_save_taxonomy_custom_fields');
add_action('edited_movie_genre', 'doomovie_save_taxonomy_custom_fields');
add_action('edited_movie_country', 'doomovie_save_taxonomy_custom_fields');
