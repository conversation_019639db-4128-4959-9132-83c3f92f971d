<?php
/**
 * Theme Customizer for DoMovie Theme
 * 
 * @package DoMovie
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add customizer settings
 */
function doomovie_customize_register($wp_customize) {
    
    // Add DoMovie Panel
    $wp_customize->add_panel('doomovie_panel', array(
        'title' => __('DoMovie Settings', 'doomovie'),
        'description' => __('Customize your movie site settings', 'doomovie'),
        'priority' => 30,
    ));
    
    // Colors Section
    $wp_customize->add_section('doomovie_colors', array(
        'title' => __('Colors', 'doomovie'),
        'panel' => 'doomovie_panel',
        'priority' => 10,
    ));
    
    // Primary Color
    $wp_customize->add_setting('doomovie_primary_color', array(
        'default' => '#e50914',
        'sanitize_callback' => 'sanitize_hex_color',
    ));
    
    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'doomovie_primary_color', array(
        'label' => __('Primary Color', 'doomovie'),
        'section' => 'doomovie_colors',
        'settings' => 'doomovie_primary_color',
    )));
    
    // Secondary Color
    $wp_customize->add_setting('doomovie_secondary_color', array(
        'default' => '#221f1f',
        'sanitize_callback' => 'sanitize_hex_color',
    ));
    
    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'doomovie_secondary_color', array(
        'label' => __('Secondary Color', 'doomovie'),
        'section' => 'doomovie_colors',
        'settings' => 'doomovie_secondary_color',
    )));
    
    // Accent Color
    $wp_customize->add_setting('doomovie_accent_color', array(
        'default' => '#f5c518',
        'sanitize_callback' => 'sanitize_hex_color',
    ));
    
    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'doomovie_accent_color', array(
        'label' => __('Accent Color', 'doomovie'),
        'section' => 'doomovie_colors',
        'settings' => 'doomovie_accent_color',
    )));
    
    // Layout Section
    $wp_customize->add_section('doomovie_layout', array(
        'title' => __('Layout', 'doomovie'),
        'panel' => 'doomovie_panel',
        'priority' => 20,
    ));
    
    // Container Width
    $wp_customize->add_setting('doomovie_container_width', array(
        'default' => '1200',
        'sanitize_callback' => 'absint',
    ));
    
    $wp_customize->add_control('doomovie_container_width', array(
        'label' => __('Container Width (px)', 'doomovie'),
        'section' => 'doomovie_layout',
        'type' => 'number',
        'input_attrs' => array(
            'min' => 960,
            'max' => 1920,
            'step' => 10,
        ),
    ));
    
    // Sidebar Position
    $wp_customize->add_setting('doomovie_sidebar_position', array(
        'default' => 'right',
        'sanitize_callback' => 'doomovie_sanitize_select',
    ));
    
    $wp_customize->add_control('doomovie_sidebar_position', array(
        'label' => __('Sidebar Position', 'doomovie'),
        'section' => 'doomovie_layout',
        'type' => 'select',
        'choices' => array(
            'left' => __('Left', 'doomovie'),
            'right' => __('Right', 'doomovie'),
            'none' => __('No Sidebar', 'doomovie'),
        ),
    ));
    
    // Homepage Section
    $wp_customize->add_section('doomovie_homepage', array(
        'title' => __('Homepage', 'doomovie'),
        'panel' => 'doomovie_panel',
        'priority' => 30,
    ));
    
    // Featured Movies Count
    $wp_customize->add_setting('doomovie_featured_count', array(
        'default' => '8',
        'sanitize_callback' => 'absint',
    ));
    
    $wp_customize->add_control('doomovie_featured_count', array(
        'label' => __('Featured Movies Count', 'doomovie'),
        'section' => 'doomovie_homepage',
        'type' => 'number',
        'input_attrs' => array(
            'min' => 4,
            'max' => 20,
            'step' => 1,
        ),
    ));
    
    // Latest Movies Count
    $wp_customize->add_setting('doomovie_latest_count', array(
        'default' => '12',
        'sanitize_callback' => 'absint',
    ));
    
    $wp_customize->add_control('doomovie_latest_count', array(
        'label' => __('Latest Movies Count', 'doomovie'),
        'section' => 'doomovie_homepage',
        'type' => 'number',
        'input_attrs' => array(
            'min' => 6,
            'max' => 24,
            'step' => 1,
        ),
    ));
    
    // Show Hero Slider
    $wp_customize->add_setting('doomovie_show_hero_slider', array(
        'default' => true,
        'sanitize_callback' => 'doomovie_sanitize_checkbox',
    ));
    
    $wp_customize->add_control('doomovie_show_hero_slider', array(
        'label' => __('Show Hero Slider', 'doomovie'),
        'section' => 'doomovie_homepage',
        'type' => 'checkbox',
    ));
    
    // Social Media Section
    $wp_customize->add_section('doomovie_social', array(
        'title' => __('Social Media', 'doomovie'),
        'panel' => 'doomovie_panel',
        'priority' => 40,
    ));
    
    // Facebook URL
    $wp_customize->add_setting('doomovie_facebook_url', array(
        'default' => '',
        'sanitize_callback' => 'esc_url_raw',
    ));
    
    $wp_customize->add_control('doomovie_facebook_url', array(
        'label' => __('Facebook URL', 'doomovie'),
        'section' => 'doomovie_social',
        'type' => 'url',
    ));
    
    // Twitter URL
    $wp_customize->add_setting('doomovie_twitter_url', array(
        'default' => '',
        'sanitize_callback' => 'esc_url_raw',
    ));
    
    $wp_customize->add_control('doomovie_twitter_url', array(
        'label' => __('Twitter URL', 'doomovie'),
        'section' => 'doomovie_social',
        'type' => 'url',
    ));
    
    // YouTube URL
    $wp_customize->add_setting('doomovie_youtube_url', array(
        'default' => '',
        'sanitize_callback' => 'esc_url_raw',
    ));
    
    $wp_customize->add_control('doomovie_youtube_url', array(
        'label' => __('YouTube URL', 'doomovie'),
        'section' => 'doomovie_social',
        'type' => 'url',
    ));
    
    // Instagram URL
    $wp_customize->add_setting('doomovie_instagram_url', array(
        'default' => '',
        'sanitize_callback' => 'esc_url_raw',
    ));
    
    $wp_customize->add_control('doomovie_instagram_url', array(
        'label' => __('Instagram URL', 'doomovie'),
        'section' => 'doomovie_social',
        'type' => 'url',
    ));
    
    // Footer Section
    $wp_customize->add_section('doomovie_footer', array(
        'title' => __('Footer', 'doomovie'),
        'panel' => 'doomovie_panel',
        'priority' => 50,
    ));
    
    // Footer Text
    $wp_customize->add_setting('doomovie_footer_text', array(
        'default' => __('© 2025 DoMovie. All rights reserved.', 'doomovie'),
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('doomovie_footer_text', array(
        'label' => __('Footer Copyright Text', 'doomovie'),
        'section' => 'doomovie_footer',
        'type' => 'text',
    ));
    
    // Footer Background Color
    $wp_customize->add_setting('doomovie_footer_bg_color', array(
        'default' => '#000000',
        'sanitize_callback' => 'sanitize_hex_color',
    ));
    
    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'doomovie_footer_bg_color', array(
        'label' => __('Footer Background Color', 'doomovie'),
        'section' => 'doomovie_footer',
        'settings' => 'doomovie_footer_bg_color',
    )));
}
add_action('customize_register', 'doomovie_customize_register');

/**
 * Sanitize select options
 */
function doomovie_sanitize_select($input, $setting) {
    $input = sanitize_key($input);
    $choices = $setting->manager->get_control($setting->id)->choices;
    return (array_key_exists($input, $choices) ? $input : $setting->default);
}

/**
 * Sanitize checkbox
 */
function doomovie_sanitize_checkbox($checked) {
    return ((isset($checked) && true == $checked) ? true : false);
}

/**
 * Output customizer CSS
 */
function doomovie_customizer_css() {
    $primary_color = get_theme_mod('doomovie_primary_color', '#e50914');
    $secondary_color = get_theme_mod('doomovie_secondary_color', '#221f1f');
    $accent_color = get_theme_mod('doomovie_accent_color', '#f5c518');
    $container_width = get_theme_mod('doomovie_container_width', '1200');
    $footer_bg_color = get_theme_mod('doomovie_footer_bg_color', '#000000');
    
    ?>
    <style type="text/css">
        :root {
            --primary-color: <?php echo esc_attr($primary_color); ?>;
            --secondary-color: <?php echo esc_attr($secondary_color); ?>;
            --accent-color: <?php echo esc_attr($accent_color); ?>;
            --container-width: <?php echo esc_attr($container_width); ?>px;
            --footer-bg-color: <?php echo esc_attr($footer_bg_color); ?>;
        }
        
        .container {
            max-width: var(--container-width);
        }
        
        .btn-primary,
        .movie-rating,
        .play-button {
            background-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: <?php echo esc_attr(doomovie_darken_color($primary_color, 20)); ?>;
        }
        
        .movie-card:hover,
        .genre-tag:hover {
            border-color: var(--primary-color);
        }
        
        .imdb-rating,
        .movie-quality {
            background-color: var(--accent-color);
        }
        
        .site-footer {
            background-color: var(--footer-bg-color);
        }
        
        .navbar,
        .movie-details-bg {
            background-color: var(--secondary-color);
        }
    </style>
    <?php
}
add_action('wp_head', 'doomovie_customizer_css');

/**
 * Helper function to darken color
 */
function doomovie_darken_color($color, $percent) {
    $color = str_replace('#', '', $color);
    $r = hexdec(substr($color, 0, 2));
    $g = hexdec(substr($color, 2, 2));
    $b = hexdec(substr($color, 4, 2));
    
    $r = max(0, min(255, $r - ($r * $percent / 100)));
    $g = max(0, min(255, $g - ($g * $percent / 100)));
    $b = max(0, min(255, $b - ($b * $percent / 100)));
    
    return sprintf('#%02x%02x%02x', $r, $g, $b);
}
