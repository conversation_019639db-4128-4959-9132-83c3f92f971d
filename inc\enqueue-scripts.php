<?php
/**
 * Enqueue Scripts and Styles for DoMovie Theme
 * 
 * @package DoMovie_Theme
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Enqueue theme styles and scripts
 */
function doomovie_enqueue_scripts() {
    // Theme version for cache busting
    $theme_version = wp_get_theme()->get('Version');
    
    // Main theme stylesheet
    wp_enqueue_style(
        'doomovie-style',
        get_stylesheet_uri(),
        array(),
        $theme_version
    );

    // Main CSS from assets folder
    if (file_exists(get_template_directory() . '/assets/css/main.css')) {
        wp_enqueue_style(
            'doomovie-main',
            get_template_directory_uri() . '/assets/css/main.css',
            array('doomovie-style'),
            $theme_version
        );
    }

    // Compiled CSS from SCSS (fallback)
    elseif (file_exists(get_template_directory() . '/dist/css/main.css')) {
        wp_enqueue_style(
            'doomovie-main',
            DOOMOVIE_THEME_URL . '/dist/css/main.css',
            array('doomovie-style'),
            $theme_version
        );
    }

    // Responsive CSS
    if (file_exists(get_template_directory() . '/dist/css/responsive.css')) {
        wp_enqueue_style(
            'doomovie-responsive',
            DOOMOVIE_THEME_URL . '/dist/css/responsive.css',
            array('doomovie-main'),
            $theme_version
        );
    }

    // Google Fonts
    wp_enqueue_style(
        'doomovie-fonts',
        'https://fonts.googleapis.com/css2?family=Noto+Sans+Thai:wght@300;400;500;600;700&display=swap',
        array(),
        null
    );

    // Font Awesome (for icons)
    wp_enqueue_style(
        'font-awesome',
        'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
        array(),
        '6.4.0'
    );

    // Swiper CSS (for sliders)
    wp_enqueue_style(
        'swiper',
        'https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css',
        array(),
        '10.0.4'
    );

    // Plyr CSS (for video player)
    wp_enqueue_style(
        'plyr',
        'https://cdn.plyr.io/3.7.8/plyr.css',
        array(),
        '3.7.8'
    );

    // Main JavaScript from assets folder
    if (file_exists(get_template_directory() . '/assets/js/main.js')) {
        wp_enqueue_script(
            'doomovie-main',
            get_template_directory_uri() . '/assets/js/main.js',
            array('jquery'),
            $theme_version,
            true
        );
    }
    // Compiled JS (fallback)
    elseif (file_exists(get_template_directory() . '/dist/js/main.js')) {
        wp_enqueue_script(
            'doomovie-main',
            DOOMOVIE_THEME_URL . '/dist/js/main.js',
            array('jquery'),
            $theme_version,
            true
        );
    } else {
        // Fallback to source files during development
        wp_enqueue_script(
            'doomovie-main',
            DOOMOVIE_THEME_URL . '/src/js/main.js',
            array('jquery'),
            $theme_version,
            true
        );
    }

    // Swiper JS
    wp_enqueue_script(
        'swiper',
        'https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js',
        array(),
        '10.0.4',
        true
    );

    // Plyr JS
    wp_enqueue_script(
        'plyr',
        'https://cdn.plyr.io/3.7.8/plyr.min.js',
        array(),
        '3.7.8',
        true
    );

    // Intersection Observer Polyfill
    wp_enqueue_script(
        'intersection-observer',
        'https://polyfill.io/v3/polyfill.min.js?features=IntersectionObserver',
        array(),
        null,
        true
    );

    // Localize script for AJAX
    wp_localize_script('doomovie-main', 'doomovie_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('doomovie_nonce'),
        'site_url' => home_url(),
        'theme_url' => DOOMOVIE_THEME_URL,
        'is_user_logged_in' => is_user_logged_in(),
        'current_user_id' => get_current_user_id(),
        'strings' => array(
            'loading' => __('กำลังโหลด...', 'doomovie-theme'),
            'error' => __('เกิดข้อผิดพลาด', 'doomovie-theme'),
            'success' => __('สำเร็จ', 'doomovie-theme'),
            'confirm' => __('คุณแน่ใจหรือไม่?', 'doomovie-theme'),
            'login_required' => __('กรุณาเข้าสู่ระบบก่อน', 'doomovie-theme'),
            'added_to_watchlist' => __('เพิ่มในรายการของคุณแล้ว', 'doomovie-theme'),
            'removed_from_watchlist' => __('ลบออกจากรายการแล้ว', 'doomovie-theme'),
            'share_copied' => __('คัดลอกลิงก์แล้ว', 'doomovie-theme'),
            'report_sent' => __('รายงานปัญหาเรียบร้อยแล้ว', 'doomovie-theme'),
        )
    ));

    // Comment reply script
    if (is_singular() && comments_open() && get_option('thread_comments')) {
        wp_enqueue_script('comment-reply');
    }

    // Conditional scripts for specific pages
    if (is_singular('movie')) {
        // Movie detail page specific scripts
        wp_enqueue_script(
            'doomovie-movie-detail',
            DOOMOVIE_THEME_URL . '/src/js/pages/movie-detail.js',
            array('doomovie-main'),
            $theme_version,
            true
        );
    }

    if (is_post_type_archive('movie') || is_tax(array('movie_genre', 'movie_country', 'movie_year'))) {
        // Movie archive page specific scripts
        wp_enqueue_script(
            'doomovie-movie-archive',
            DOOMOVIE_THEME_URL . '/src/js/pages/movie-archive.js',
            array('doomovie-main'),
            $theme_version,
            true
        );
    }

    if (is_search()) {
        // Search page specific scripts
        wp_enqueue_script(
            'doomovie-search',
            DOOMOVIE_THEME_URL . '/src/js/pages/search.js',
            array('doomovie-main'),
            $theme_version,
            true
        );
    }
}
add_action('wp_enqueue_scripts', 'doomovie_enqueue_scripts');

/**
 * Enqueue admin styles and scripts
 */
function doomovie_admin_enqueue_scripts($hook) {
    // Only load on specific admin pages
    $allowed_hooks = array(
        'post.php',
        'post-new.php',
        'edit.php',
        'toplevel_page_doomovie-settings'
    );

    if (!in_array($hook, $allowed_hooks)) {
        return;
    }

    $theme_version = wp_get_theme()->get('Version');

    // Admin CSS
    wp_enqueue_style(
        'doomovie-admin',
        DOOMOVIE_THEME_URL . '/assets/css/admin.css',
        array(),
        $theme_version
    );

    // Admin JS
    wp_enqueue_script(
        'doomovie-admin',
        DOOMOVIE_THEME_URL . '/assets/js/admin.js',
        array('jquery', 'wp-color-picker'),
        $theme_version,
        true
    );

    // Color picker
    wp_enqueue_style('wp-color-picker');

    // Media uploader
    if (in_array($hook, array('post.php', 'post-new.php'))) {
        wp_enqueue_media();
    }

    // Localize admin script
    wp_localize_script('doomovie-admin', 'doomovie_admin', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('doomovie_admin_nonce'),
        'strings' => array(
            'confirm_delete' => __('คุณแน่ใจที่จะลบหรือไม่?', 'doomovie-theme'),
            'select_image' => __('เลือกรูปภาพ', 'doomovie-theme'),
            'use_image' => __('ใช้รูปภาพนี้', 'doomovie-theme'),
        )
    ));
}
add_action('admin_enqueue_scripts', 'doomovie_admin_enqueue_scripts');

/**
 * Add preload hints for performance
 */
function doomovie_add_preload_hints() {
    // Preload critical CSS
    echo '<link rel="preload" href="' . get_stylesheet_uri() . '" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">' . "\n";
    
    // Preload Google Fonts
    echo '<link rel="preconnect" href="https://fonts.googleapis.com">' . "\n";
    echo '<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>' . "\n";
    
    // Preload critical images
    $logo_url = DOOMOVIE_THEME_URL . '/assets/images/logo/doomovie-logo.png';
    if (file_exists(get_template_directory() . '/assets/images/logo/doomovie-logo.png')) {
        echo '<link rel="preload" href="' . $logo_url . '" as="image">' . "\n";
    }
}
add_action('wp_head', 'doomovie_add_preload_hints', 1);

/**
 * Add critical CSS inline for performance
 */
function doomovie_add_critical_css() {
    $critical_css_file = get_template_directory() . '/dist/css/critical.css';
    
    if (file_exists($critical_css_file)) {
        echo '<style id="doomovie-critical-css">';
        echo file_get_contents($critical_css_file);
        echo '</style>' . "\n";
    }
}
add_action('wp_head', 'doomovie_add_critical_css', 2);

/**
 * Remove unnecessary scripts and styles
 */
function doomovie_dequeue_unnecessary_scripts() {
    // Remove jQuery Migrate if not needed
    if (!is_admin()) {
        wp_deregister_script('jquery-migrate');
    }

    // Remove emoji scripts
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');
    remove_action('admin_print_scripts', 'print_emoji_detection_script');
    remove_action('admin_print_styles', 'print_emoji_styles');

    // Remove unnecessary WordPress features
    remove_action('wp_head', 'wp_generator');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'rsd_link');
    remove_action('wp_head', 'wp_shortlink_wp_head');
}
add_action('init', 'doomovie_dequeue_unnecessary_scripts');

/**
 * Add async/defer attributes to scripts
 */
function doomovie_add_async_defer_attributes($tag, $handle, $src) {
    // Scripts to defer
    $defer_scripts = array(
        'doomovie-main',
        'swiper',
        'plyr'
    );

    // Scripts to async
    $async_scripts = array(
        'font-awesome',
        'intersection-observer'
    );

    if (in_array($handle, $defer_scripts)) {
        return str_replace('<script ', '<script defer ', $tag);
    }

    if (in_array($handle, $async_scripts)) {
        return str_replace('<script ', '<script async ', $tag);
    }

    return $tag;
}
add_filter('script_loader_tag', 'doomovie_add_async_defer_attributes', 10, 3);

/**
 * Add DNS prefetch for external resources
 */
function doomovie_add_dns_prefetch() {
    echo '<link rel="dns-prefetch" href="//fonts.googleapis.com">' . "\n";
    echo '<link rel="dns-prefetch" href="//fonts.gstatic.com">' . "\n";
    echo '<link rel="dns-prefetch" href="//cdnjs.cloudflare.com">' . "\n";
    echo '<link rel="dns-prefetch" href="//cdn.jsdelivr.net">' . "\n";
    echo '<link rel="dns-prefetch" href="//cdn.plyr.io">' . "\n";
}
add_action('wp_head', 'doomovie_add_dns_prefetch', 0);

/**
 * Optimize CSS delivery
 */
function doomovie_optimize_css_delivery() {
    // Add media="print" to non-critical CSS and load them asynchronously
    ?>
    <script>
    // Load non-critical CSS asynchronously
    function loadCSS(href, before, media) {
        var doc = window.document;
        var ss = doc.createElement("link");
        var ref;
        if (before) {
            ref = before;
        } else {
            var refs = (doc.body || doc.getElementsByTagName("head")[0]).childNodes;
            ref = refs[refs.length - 1];
        }
        var sheets = doc.styleSheets;
        ss.rel = "stylesheet";
        ss.href = href;
        ss.media = "only x";
        function ready(cb) {
            if (doc.body) {
                return cb();
            }
            setTimeout(function() {
                ready(cb);
            });
        }
        ready(function() {
            ref.parentNode.insertBefore(ss, (before ? ref : ref.nextSibling));
        });
        var onloadcssdefined = function(cb) {
            var resolvedHref = ss.href;
            var i = sheets.length;
            while (i--) {
                if (sheets[i].href === resolvedHref) {
                    return cb();
                }
            }
            setTimeout(function() {
                onloadcssdefined(cb);
            });
        };
        function loadCB() {
            if (ss.addEventListener) {
                ss.removeEventListener("load", loadCB);
            }
            ss.media = media || "all";
        }
        if (ss.addEventListener) {
            ss.addEventListener("load", loadCB);
        }
        ss.onloadcssdefined = onloadcssdefined;
        onloadcssdefined(loadCB);
        return ss;
    }
    </script>
    <?php
}
add_action('wp_head', 'doomovie_optimize_css_delivery', 100);

/**
 * Add service worker for caching (if available)
 */
function doomovie_add_service_worker() {
    $sw_file = get_template_directory() . '/sw.js';
    
    if (file_exists($sw_file)) {
        ?>
        <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('<?php echo DOOMOVIE_THEME_URL; ?>/sw.js')
                .then(function(registration) {
                    console.log('SW registered: ', registration);
                }).catch(function(registrationError) {
                    console.log('SW registration failed: ', registrationError);
                });
            });
        }
        </script>
        <?php
    }
}
add_action('wp_footer', 'doomovie_add_service_worker');
