<?php
/**
 * Sample Data Generator for DoMovie Theme
 * Creates realistic movie data for demonstration
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Generate sample movie data with categories
 */
function doomovie_create_sample_data() {
    // Sample movie data with real movie information and categories
    $sample_movies = array(
        // หนังใหม่ (New Movies)
        array(
            'title' => 'Deadpool & Wolverine',
            'year' => '2024',
            'rating' => 'R',
            'quality' => '4K',
            'imdb_rating' => '8.1',
            'duration' => '127 min',
            'genres' => array('Action', 'Comedy', 'Adventure'),
            'categories' => array('new-movies', 'hero-movies'),
            'description' => '<PERSON><PERSON> is offered a place in the Marvel Cinematic Universe by the Time Variance Authority, but instead recruits a variant of <PERSON> to save his universe from extinction.',
            'poster' => 'https://image.tmdb.org/t/p/w500/8cdWjvZQUExUUTzyp4t6EDMubfO.jpg'
        ),
        array(
            'title' => 'Inside Out 2',
            'year' => '2024',
            'rating' => 'PG',
            'quality' => '4K',
            'imdb_rating' => '7.8',
            'duration' => '96 min',
            'genres' => array('Animation', 'Family', 'Comedy'),
            'categories' => array('new-movies'),
            'description' => 'A sequel that features Riley entering puberty and experiencing brand new, more complex emotions as a result.',
            'poster' => 'https://image.tmdb.org/t/p/w500/vpnVM9B6NMmQpWeZvzLvDESb2QY.jpg'
        ),
        array(
            'title' => 'Bad Boys: Ride or Die',
            'year' => '2024',
            'rating' => 'R',
            'quality' => '4K',
            'imdb_rating' => '6.8',
            'duration' => '115 min',
            'genres' => array('Action', 'Comedy', 'Crime'),
            'categories' => array('new-movies'),
            'description' => 'When their late police captain gets linked to drug cartels, wisecracking Miami cops Mike Lowrey and Marcus Burnett embark on a dangerous mission to clear his name.',
            'poster' => 'https://image.tmdb.org/t/p/w500/oGythE98MYleE6mZlGs5oBGkux1.jpg'
        ),

        // ซีรี่ย์ (Series)
        array(
            'title' => 'The Bear Season 3',
            'year' => '2024',
            'rating' => 'TV-MA',
            'quality' => '4K',
            'imdb_rating' => '8.7',
            'duration' => '30 min/ep',
            'genres' => array('Comedy', 'Drama'),
            'categories' => array('series'),
            'description' => 'Carmen "Carmy" Berzatto, a young chef from the fine dining world, comes home to Chicago to run his deceased brother\'s Italian beef sandwich shop.',
            'poster' => 'https://image.tmdb.org/t/p/w500/7zguTdip2ib6Zs8sxdl9sOm6lbd.jpg'
        ),
        array(
            'title' => 'House of the Dragon Season 2',
            'year' => '2024',
            'rating' => 'TV-MA',
            'quality' => '4K',
            'imdb_rating' => '8.4',
            'duration' => '60 min/ep',
            'genres' => array('Drama', 'Fantasy', 'Action'),
            'categories' => array('series'),
            'description' => 'The Targaryen civil war continues. Rhaenyra and Daemon return to Dragonstone to plan their next move.',
            'poster' => 'https://image.tmdb.org/t/p/w500/7QMsOTMUswlwxJP0rTTZfmz2tX2.jpg'
        ),
        array(
            'title' => 'Stranger Things Season 4',
            'year' => '2024',
            'rating' => 'TV-14',
            'quality' => '4K',
            'imdb_rating' => '8.9',
            'duration' => '75 min/ep',
            'genres' => array('Drama', 'Fantasy', 'Horror'),
            'categories' => array('series'),
            'description' => 'When a young boy vanishes, a small town uncovers a mystery involving secret experiments, terrifying supernatural forces and one strange little girl.',
            'poster' => 'https://image.tmdb.org/t/p/w500/49WJfeN0moxb9IPfGn8AIqMGskD.jpg'
        ),

        // หนังไทย (Thai Movies)
        array(
            'title' => 'ธี่หยด 2',
            'year' => '2024',
            'rating' => 'R',
            'quality' => '4K',
            'imdb_rating' => '6.5',
            'duration' => '111 min',
            'genres' => array('Horror', 'Thriller'),
            'categories' => array('thai-movies'),
            'description' => 'ภาคต่อของหนังสยองขวัญไทยยอดนิยม เรื่องราวของผีโขมดที่กลับมาอีกครั้ง',
            'poster' => 'https://image.tmdb.org/t/p/w500/aKuPPnVwgwDwjKYSLbOqmTRTOTQ.jpg'
        ),
        array(
            'title' => 'วัยหนุ่ม 2544',
            'year' => '2024',
            'rating' => 'PG-13',
            'quality' => '4K',
            'imdb_rating' => '7.2',
            'duration' => '125 min',
            'genres' => array('Drama', 'Comedy'),
            'categories' => array('thai-movies'),
            'description' => 'เรื่องราวของเด็กหนุ่มในยุค 90 ที่ต้องเผชิญกับการเปลี่ยนแปลงของสังคมไทย',
            'poster' => 'https://image.tmdb.org/t/p/w500/bKpPTVrNQjsqZ8uybnXvdaXAYXQ.jpg'
        ),
        array(
            'title' => 'สงคราม ส่งด่วน',
            'year' => '2024',
            'rating' => 'PG-13',
            'quality' => '4K',
            'imdb_rating' => '6.8',
            'duration' => '108 min',
            'genres' => array('Comedy', 'Action'),
            'categories' => array('thai-movies'),
            'description' => 'ศึกธุรกิจส่งของระหว่างบริษัทขนส่งสองแห่งที่แข่งขันกันอย่างดุเดือด',
            'poster' => 'https://image.tmdb.org/t/p/w500/cKpPTVrNQjsqZ8uybnXvdaXAYXQ.jpg'
        ),

        // หนังฮีโร่ (Hero Movies)
        array(
            'title' => 'Spider-Man: Across the Spider-Verse',
            'year' => '2023',
            'rating' => 'PG',
            'quality' => '4K',
            'imdb_rating' => '8.7',
            'duration' => '140 min',
            'genres' => array('Animation', 'Action', 'Adventure'),
            'categories' => array('hero-movies'),
            'description' => 'Miles Morales catapults across the Multiverse, where he encounters a team of Spider-People charged with protecting its very existence.',
            'poster' => 'https://image.tmdb.org/t/p/w500/8Vt6mWEReuy4Of61Lnj5Xj704m8.jpg'
        ),
        array(
            'title' => 'Guardians of the Galaxy Vol. 3',
            'year' => '2023',
            'rating' => 'PG-13',
            'quality' => '4K',
            'imdb_rating' => '7.9',
            'duration' => '150 min',
            'genres' => array('Action', 'Adventure', 'Comedy'),
            'categories' => array('hero-movies'),
            'description' => 'Still reeling from the loss of Gamora, Peter Quill rallies his team to defend the universe and protect one of their own.',
            'poster' => 'https://image.tmdb.org/t/p/w500/5YZbUmjbMa3ClvSW1Wj2yGmapfI.jpg'
        ),
        array(
            'title' => 'The Flash',
            'year' => '2023',
            'rating' => 'PG-13',
            'quality' => '4K',
            'imdb_rating' => '6.7',
            'duration' => '144 min',
            'genres' => array('Action', 'Adventure', 'Fantasy'),
            'categories' => array('hero-movies'),
            'description' => 'Barry Allen uses his super speed to change the past, but his attempt to save his family creates a world without super heroes.',
            'poster' => 'https://image.tmdb.org/t/p/w500/rktDFPbfHfUbArZ6OOOKsXcv0Bm.jpg'
        ),
        array(
            'title' => 'Ant-Man and the Wasp: Quantumania',
            'year' => '2023',
            'rating' => 'PG-13',
            'quality' => '4K',
            'imdb_rating' => '6.1',
            'duration' => '124 min',
            'genres' => array('Action', 'Adventure', 'Comedy'),
            'categories' => array('hero-movies'),
            'description' => 'Scott Lang and Hope Van Dyne are dragged into the Quantum Realm, along with Hope\'s parents and Scott\'s daughter Cassie.',
            'poster' => 'https://image.tmdb.org/t/p/w500/ngl2FKBlU4fhbdsrtdom9LVLBXw.jpg'
        ),

        // เพิ่มหนังอื่นๆ
        array(
            'title' => 'Avatar: The Way of Water',
            'year' => '2022',
            'rating' => 'PG-13',
            'quality' => '4K',
            'imdb_rating' => '7.6',
            'duration' => '192 min',
            'genres' => array('Action', 'Adventure', 'Fantasy'),
            'categories' => array('new-movies'),
            'description' => 'Jake Sully lives with his newfound family formed on the extrasolar moon Pandora. Once a familiar threat returns to finish what was previously started, Jake must work with Neytiri and the army of the Na\'vi race to protect their planet.',
            'poster' => 'https://image.tmdb.org/t/p/w500/t6HIqrRAclMCA60NsSmeqe9RmNV.jpg'
        ),
        array(
            'title' => 'Top Gun: Maverick',
            'year' => '2022',
            'rating' => 'PG-13',
            'quality' => '4K',
            'imdb_rating' => '8.3',
            'duration' => '130 min',
            'genres' => array('Action', 'Drama'),
            'description' => 'After thirty years, Maverick is still pushing the envelope as a top naval aviator, but must confront ghosts of his past when he leads TOP GUN\'s elite graduates on a mission that demands the ultimate sacrifice from those chosen to fly it.',
            'poster' => 'https://image.tmdb.org/t/p/w500/62HCnUTziyWcpDaBO2i1DX17ljH.jpg'
        ),
        array(
            'title' => 'Black Panther: Wakanda Forever',
            'year' => '2022',
            'rating' => 'PG-13',
            'quality' => '4K',
            'imdb_rating' => '6.7',
            'duration' => '161 min',
            'genres' => array('Action', 'Adventure', 'Drama'),
            'description' => 'The people of Wakanda fight to protect their home from intervening world powers as they mourn the death of King T\'Challa.',
            'poster' => 'https://image.tmdb.org/t/p/w500/sv1xJUazXeYqALzczSZ3O6nkH75.jpg'
        ),
        array(
            'title' => 'Spider-Man: No Way Home',
            'year' => '2021',
            'rating' => 'PG-13',
            'quality' => '4K',
            'imdb_rating' => '8.2',
            'duration' => '148 min',
            'genres' => array('Action', 'Adventure', 'Fantasy'),
            'description' => 'With Spider-Man\'s identity now revealed, Peter asks Doctor Strange for help. When a spell goes wrong, dangerous foes from other worlds start to appear, forcing Peter to discover what it truly means to be Spider-Man.',
            'poster' => 'https://image.tmdb.org/t/p/w500/1g0dhYtq4irTY1GPXvft6k4YLjm.jpg'
        ),
        array(
            'title' => 'The Batman',
            'year' => '2022',
            'rating' => 'PG-13',
            'quality' => '4K',
            'imdb_rating' => '7.8',
            'duration' => '176 min',
            'genres' => array('Action', 'Crime', 'Drama'),
            'description' => 'When the Riddler, a sadistic serial killer, begins murdering key political figures in Gotham, Batman is forced to investigate the city\'s hidden corruption and question his family\'s involvement.',
            'poster' => 'https://image.tmdb.org/t/p/w500/b0PlSFdDwbyK0cf5RxwDpaOJQvQ.jpg'
        ),
        array(
            'title' => 'Doctor Strange in the Multiverse of Madness',
            'year' => '2022',
            'rating' => 'PG-13',
            'quality' => '4K',
            'imdb_rating' => '6.9',
            'duration' => '126 min',
            'genres' => array('Action', 'Adventure', 'Fantasy'),
            'description' => 'Doctor Strange teams up with a mysterious teenage girl from his dreams who can travel across multiverses, to battle multiple threats, including other-universe versions of himself.',
            'poster' => 'https://image.tmdb.org/t/p/w500/9Gtg2DzBhmYamXBS1hKAhiwbBKS.jpg'
        ),
        array(
            'title' => 'Jurassic World Dominion',
            'year' => '2022',
            'rating' => 'PG-13',
            'quality' => '4K',
            'imdb_rating' => '5.6',
            'duration' => '147 min',
            'genres' => array('Action', 'Adventure', 'Sci-Fi'),
            'description' => 'Four years after the destruction of Isla Nublar, dinosaurs now live and hunt alongside humans all over the world. This fragile balance will reshape the future and determine, once and for all, whether human beings are to remain the apex predators.',
            'poster' => 'https://image.tmdb.org/t/p/w500/kAVRgw7GgK1CfYEJq8ME6EvRIgU.jpg'
        ),
        array(
            'title' => 'Thor: Love and Thunder',
            'year' => '2022',
            'rating' => 'PG-13',
            'quality' => '4K',
            'imdb_rating' => '6.2',
            'duration' => '119 min',
            'genres' => array('Action', 'Adventure', 'Comedy'),
            'description' => 'Thor enlists the help of Valkyrie, Korg and ex-girlfriend Jane Foster to fight Gorr the God Butcher, who intends to make the gods extinct.',
            'poster' => 'https://image.tmdb.org/t/p/w500/pIkRyD18kl4FhoCNQuWxWu5cBLM.jpg'
        ),
        array(
            'title' => 'Minions: The Rise of Gru',
            'year' => '2022',
            'rating' => 'PG',
            'quality' => '4K',
            'imdb_rating' => '6.5',
            'duration' => '87 min',
            'genres' => array('Animation', 'Adventure', 'Comedy'),
            'description' => 'The untold story of one twelve-year-old\'s dream to become the world\'s greatest supervillain.',
            'poster' => 'https://image.tmdb.org/t/p/w500/wKiOkZTN9lUUUNZLmtnwubZYONg.jpg'
        ),
        array(
            'title' => 'Lightyear',
            'year' => '2022',
            'rating' => 'PG',
            'quality' => '4K',
            'imdb_rating' => '6.1',
            'duration' => '105 min',
            'genres' => array('Animation', 'Action', 'Adventure'),
            'description' => 'The story of Buzz Lightyear and his adventures to infinity and beyond.',
            'poster' => 'https://image.tmdb.org/t/p/w500/ox4goZd956BxqJH6iLwhWPL9ct4.jpg'
        ),
        array(
            'title' => 'Nope',
            'year' => '2022',
            'rating' => 'R',
            'quality' => '4K',
            'imdb_rating' => '6.8',
            'duration' => '130 min',
            'genres' => array('Horror', 'Mystery', 'Sci-Fi'),
            'description' => 'The residents of a lonely gulch in inland California bear witness to an uncanny and chilling discovery.',
            'poster' => 'https://image.tmdb.org/t/p/w500/AcKVlWaNVVVFQwro3nLXqPljcYA.jpg'
        ),
        array(
            'title' => 'Bullet Train',
            'year' => '2022',
            'rating' => 'R',
            'quality' => '4K',
            'imdb_rating' => '7.3',
            'duration' => '127 min',
            'genres' => array('Action', 'Comedy', 'Thriller'),
            'description' => 'Five assassins aboard a fast moving bullet train find out their missions have something in common.',
            'poster' => 'https://image.tmdb.org/t/p/w500/j8szC8OgrejpCnhyaKEaQYmZzPb.jpg'
        )
    );

    return $sample_movies;
}

/**
 * Create sample movie posts
 */
function doomovie_insert_sample_movies() {
    $sample_movies = doomovie_create_sample_data();
    $created_posts = array();

    foreach ($sample_movies as $movie_data) {
        // Check if movie already exists
        $existing_post = get_page_by_title($movie_data['title'], OBJECT, 'movie');
        if ($existing_post) {
            continue; // Skip if already exists
        }

        // Create the post
        $post_data = array(
            'post_title' => $movie_data['title'],
            'post_content' => $movie_data['description'],
            'post_excerpt' => wp_trim_words($movie_data['description'], 25),
            'post_status' => 'publish',
            'post_type' => 'movie',
            'post_author' => 1
        );

        $post_id = wp_insert_post($post_data);

        if ($post_id && !is_wp_error($post_id)) {
            // Add movie metadata
            update_post_meta($post_id, '_movie_year', $movie_data['year']);
            update_post_meta($post_id, '_movie_rating', $movie_data['rating']);
            update_post_meta($post_id, '_movie_quality', $movie_data['quality']);
            update_post_meta($post_id, '_movie_imdb_rating', $movie_data['imdb_rating']);
            update_post_meta($post_id, '_movie_duration', $movie_data['duration']);
            update_post_meta($post_id, '_movie_poster', $movie_data['poster']);
            update_post_meta($post_id, '_movie_views', rand(1000, 50000));
            update_post_meta($post_id, '_featured_movie', rand(0, 1));

            // Add genres
            if (!empty($movie_data['genres'])) {
                $genre_ids = array();
                foreach ($movie_data['genres'] as $genre_name) {
                    $genre = get_term_by('name', $genre_name, 'movie_genre');
                    if (!$genre) {
                        $genre = wp_insert_term($genre_name, 'movie_genre');
                        if (!is_wp_error($genre)) {
                            $genre_ids[] = $genre['term_id'];
                        }
                    } else {
                        $genre_ids[] = $genre->term_id;
                    }
                }
                if (!empty($genre_ids)) {
                    wp_set_post_terms($post_id, $genre_ids, 'movie_genre');
                }
            }

            // Add categories
            if (!empty($movie_data['categories'])) {
                $category_ids = array();
                foreach ($movie_data['categories'] as $category_name) {
                    $category = get_term_by('slug', $category_name, 'movie_category');
                    if (!$category) {
                        // Create category with proper names
                        $category_names = array(
                            'new-movies' => 'หนังใหม่',
                            'series' => 'ซีรี่ย์',
                            'thai-movies' => 'หนังไทย',
                            'hero-movies' => 'หนังฮีโร่'
                        );
                        $category_name_th = isset($category_names[$category_name]) ? $category_names[$category_name] : $category_name;

                        $category = wp_insert_term($category_name_th, 'movie_category', array(
                            'slug' => $category_name
                        ));
                        if (!is_wp_error($category)) {
                            $category_ids[] = $category['term_id'];
                        }
                    } else {
                        $category_ids[] = $category->term_id;
                    }
                }
                if (!empty($category_ids)) {
                    wp_set_post_terms($post_id, $category_ids, 'movie_category');
                }
            }

            $created_posts[] = $post_id;
        }
    }

    return $created_posts;
}

/**
 * Initialize sample data on theme activation
 */
function doomovie_init_sample_data() {
    // Only create sample data if no movies exist
    $existing_movies = get_posts(array(
        'post_type' => 'movie',
        'posts_per_page' => 1,
        'post_status' => 'publish'
    ));

    if (empty($existing_movies)) {
        doomovie_insert_sample_movies();
    }
}

// Hook to create sample data
add_action('after_switch_theme', 'doomovie_init_sample_data');

/**
 * Admin function to regenerate sample data
 */
function doomovie_regenerate_sample_data() {
    if (current_user_can('manage_options')) {
        doomovie_insert_sample_movies();
        wp_redirect(admin_url('themes.php?sample-data=created'));
        exit;
    }
}

// Add admin notice for sample data creation
add_action('admin_notices', function() {
    if (isset($_GET['sample-data']) && $_GET['sample-data'] === 'created') {
        echo '<div class="notice notice-success is-dismissible"><p>Sample movie data has been created successfully!</p></div>';
    }
});
?>
