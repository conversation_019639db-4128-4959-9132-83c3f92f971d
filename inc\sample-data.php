<?php
/**
 * Sample Data Generator for DoMovie Theme
 * Creates realistic movie data for demonstration
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Generate sample movie data
 */
function doomovie_create_sample_data() {
    // Sample movie data with real movie information
    $sample_movies = array(
        array(
            'title' => 'Avatar: The Way of Water',
            'year' => '2022',
            'rating' => 'PG-13',
            'quality' => '4K',
            'imdb_rating' => '7.6',
            'duration' => '192 min',
            'genres' => array('Action', 'Adventure', 'Fantasy'),
            'description' => '<PERSON> lives with his newfound family formed on the extrasolar moon Pandora. Once a familiar threat returns to finish what was previously started, <PERSON> must work with <PERSON><PERSON><PERSON><PERSON> and the army of the Na\'vi race to protect their planet.',
            'poster' => 'https://image.tmdb.org/t/p/w500/t6HIqrRAclMCA60NsSmeqe9RmNV.jpg'
        ),
        array(
            'title' => 'Top Gun: Maverick',
            'year' => '2022',
            'rating' => 'PG-13',
            'quality' => '4K',
            'imdb_rating' => '8.3',
            'duration' => '130 min',
            'genres' => array('Action', 'Drama'),
            'description' => 'After thirty years, Maverick is still pushing the envelope as a top naval aviator, but must confront ghosts of his past when he leads TOP GUN\'s elite graduates on a mission that demands the ultimate sacrifice from those chosen to fly it.',
            'poster' => 'https://image.tmdb.org/t/p/w500/62HCnUTziyWcpDaBO2i1DX17ljH.jpg'
        ),
        array(
            'title' => 'Black Panther: Wakanda Forever',
            'year' => '2022',
            'rating' => 'PG-13',
            'quality' => '4K',
            'imdb_rating' => '6.7',
            'duration' => '161 min',
            'genres' => array('Action', 'Adventure', 'Drama'),
            'description' => 'The people of Wakanda fight to protect their home from intervening world powers as they mourn the death of King T\'Challa.',
            'poster' => 'https://image.tmdb.org/t/p/w500/sv1xJUazXeYqALzczSZ3O6nkH75.jpg'
        ),
        array(
            'title' => 'Spider-Man: No Way Home',
            'year' => '2021',
            'rating' => 'PG-13',
            'quality' => '4K',
            'imdb_rating' => '8.2',
            'duration' => '148 min',
            'genres' => array('Action', 'Adventure', 'Fantasy'),
            'description' => 'With Spider-Man\'s identity now revealed, Peter asks Doctor Strange for help. When a spell goes wrong, dangerous foes from other worlds start to appear, forcing Peter to discover what it truly means to be Spider-Man.',
            'poster' => 'https://image.tmdb.org/t/p/w500/1g0dhYtq4irTY1GPXvft6k4YLjm.jpg'
        ),
        array(
            'title' => 'The Batman',
            'year' => '2022',
            'rating' => 'PG-13',
            'quality' => '4K',
            'imdb_rating' => '7.8',
            'duration' => '176 min',
            'genres' => array('Action', 'Crime', 'Drama'),
            'description' => 'When the Riddler, a sadistic serial killer, begins murdering key political figures in Gotham, Batman is forced to investigate the city\'s hidden corruption and question his family\'s involvement.',
            'poster' => 'https://image.tmdb.org/t/p/w500/b0PlSFdDwbyK0cf5RxwDpaOJQvQ.jpg'
        ),
        array(
            'title' => 'Doctor Strange in the Multiverse of Madness',
            'year' => '2022',
            'rating' => 'PG-13',
            'quality' => '4K',
            'imdb_rating' => '6.9',
            'duration' => '126 min',
            'genres' => array('Action', 'Adventure', 'Fantasy'),
            'description' => 'Doctor Strange teams up with a mysterious teenage girl from his dreams who can travel across multiverses, to battle multiple threats, including other-universe versions of himself.',
            'poster' => 'https://image.tmdb.org/t/p/w500/9Gtg2DzBhmYamXBS1hKAhiwbBKS.jpg'
        ),
        array(
            'title' => 'Jurassic World Dominion',
            'year' => '2022',
            'rating' => 'PG-13',
            'quality' => '4K',
            'imdb_rating' => '5.6',
            'duration' => '147 min',
            'genres' => array('Action', 'Adventure', 'Sci-Fi'),
            'description' => 'Four years after the destruction of Isla Nublar, dinosaurs now live and hunt alongside humans all over the world. This fragile balance will reshape the future and determine, once and for all, whether human beings are to remain the apex predators.',
            'poster' => 'https://image.tmdb.org/t/p/w500/kAVRgw7GgK1CfYEJq8ME6EvRIgU.jpg'
        ),
        array(
            'title' => 'Thor: Love and Thunder',
            'year' => '2022',
            'rating' => 'PG-13',
            'quality' => '4K',
            'imdb_rating' => '6.2',
            'duration' => '119 min',
            'genres' => array('Action', 'Adventure', 'Comedy'),
            'description' => 'Thor enlists the help of Valkyrie, Korg and ex-girlfriend Jane Foster to fight Gorr the God Butcher, who intends to make the gods extinct.',
            'poster' => 'https://image.tmdb.org/t/p/w500/pIkRyD18kl4FhoCNQuWxWu5cBLM.jpg'
        ),
        array(
            'title' => 'Minions: The Rise of Gru',
            'year' => '2022',
            'rating' => 'PG',
            'quality' => '4K',
            'imdb_rating' => '6.5',
            'duration' => '87 min',
            'genres' => array('Animation', 'Adventure', 'Comedy'),
            'description' => 'The untold story of one twelve-year-old\'s dream to become the world\'s greatest supervillain.',
            'poster' => 'https://image.tmdb.org/t/p/w500/wKiOkZTN9lUUUNZLmtnwubZYONg.jpg'
        ),
        array(
            'title' => 'Lightyear',
            'year' => '2022',
            'rating' => 'PG',
            'quality' => '4K',
            'imdb_rating' => '6.1',
            'duration' => '105 min',
            'genres' => array('Animation', 'Action', 'Adventure'),
            'description' => 'The story of Buzz Lightyear and his adventures to infinity and beyond.',
            'poster' => 'https://image.tmdb.org/t/p/w500/ox4goZd956BxqJH6iLwhWPL9ct4.jpg'
        ),
        array(
            'title' => 'Nope',
            'year' => '2022',
            'rating' => 'R',
            'quality' => '4K',
            'imdb_rating' => '6.8',
            'duration' => '130 min',
            'genres' => array('Horror', 'Mystery', 'Sci-Fi'),
            'description' => 'The residents of a lonely gulch in inland California bear witness to an uncanny and chilling discovery.',
            'poster' => 'https://image.tmdb.org/t/p/w500/AcKVlWaNVVVFQwro3nLXqPljcYA.jpg'
        ),
        array(
            'title' => 'Bullet Train',
            'year' => '2022',
            'rating' => 'R',
            'quality' => '4K',
            'imdb_rating' => '7.3',
            'duration' => '127 min',
            'genres' => array('Action', 'Comedy', 'Thriller'),
            'description' => 'Five assassins aboard a fast moving bullet train find out their missions have something in common.',
            'poster' => 'https://image.tmdb.org/t/p/w500/j8szC8OgrejpCnhyaKEaQYmZzPb.jpg'
        )
    );

    return $sample_movies;
}

/**
 * Create sample movie posts
 */
function doomovie_insert_sample_movies() {
    $sample_movies = doomovie_create_sample_data();
    $created_posts = array();

    foreach ($sample_movies as $movie_data) {
        // Check if movie already exists
        $existing_post = get_page_by_title($movie_data['title'], OBJECT, 'movie');
        if ($existing_post) {
            continue; // Skip if already exists
        }

        // Create the post
        $post_data = array(
            'post_title' => $movie_data['title'],
            'post_content' => $movie_data['description'],
            'post_excerpt' => wp_trim_words($movie_data['description'], 25),
            'post_status' => 'publish',
            'post_type' => 'movie',
            'post_author' => 1
        );

        $post_id = wp_insert_post($post_data);

        if ($post_id && !is_wp_error($post_id)) {
            // Add movie metadata
            update_post_meta($post_id, '_movie_year', $movie_data['year']);
            update_post_meta($post_id, '_movie_rating', $movie_data['rating']);
            update_post_meta($post_id, '_movie_quality', $movie_data['quality']);
            update_post_meta($post_id, '_movie_imdb_rating', $movie_data['imdb_rating']);
            update_post_meta($post_id, '_movie_duration', $movie_data['duration']);
            update_post_meta($post_id, '_movie_poster', $movie_data['poster']);
            update_post_meta($post_id, '_movie_views', rand(1000, 50000));
            update_post_meta($post_id, '_featured_movie', rand(0, 1));

            // Add genres
            if (!empty($movie_data['genres'])) {
                $genre_ids = array();
                foreach ($movie_data['genres'] as $genre_name) {
                    $genre = get_term_by('name', $genre_name, 'movie_genre');
                    if (!$genre) {
                        $genre = wp_insert_term($genre_name, 'movie_genre');
                        if (!is_wp_error($genre)) {
                            $genre_ids[] = $genre['term_id'];
                        }
                    } else {
                        $genre_ids[] = $genre->term_id;
                    }
                }
                if (!empty($genre_ids)) {
                    wp_set_post_terms($post_id, $genre_ids, 'movie_genre');
                }
            }

            $created_posts[] = $post_id;
        }
    }

    return $created_posts;
}

/**
 * Initialize sample data on theme activation
 */
function doomovie_init_sample_data() {
    // Only create sample data if no movies exist
    $existing_movies = get_posts(array(
        'post_type' => 'movie',
        'posts_per_page' => 1,
        'post_status' => 'publish'
    ));

    if (empty($existing_movies)) {
        doomovie_insert_sample_movies();
    }
}

// Hook to create sample data
add_action('after_switch_theme', 'doomovie_init_sample_data');

/**
 * Admin function to regenerate sample data
 */
function doomovie_regenerate_sample_data() {
    if (current_user_can('manage_options')) {
        doomovie_insert_sample_movies();
        wp_redirect(admin_url('themes.php?sample-data=created'));
        exit;
    }
}

// Add admin notice for sample data creation
add_action('admin_notices', function() {
    if (isset($_GET['sample-data']) && $_GET['sample-data'] === 'created') {
        echo '<div class="notice notice-success is-dismissible"><p>Sample movie data has been created successfully!</p></div>';
    }
});
?>
