<?php
/**
 * Security Functions for DoMovie Theme
 * 
 * @package DoMovie
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Remove WordPress version from head and feeds
 */
function doomovie_remove_wp_version() {
    return '';
}
add_filter('the_generator', 'doomovie_remove_wp_version');

/**
 * Hide WordPress version from scripts and styles
 */
function doomovie_remove_version_from_scripts($src) {
    if (strpos($src, 'ver=' . get_bloginfo('version'))) {
        $src = remove_query_arg('ver', $src);
    }
    return $src;
}
add_filter('script_loader_src', 'doomovie_remove_version_from_scripts', 15, 1);
add_filter('style_loader_src', 'doomovie_remove_version_from_scripts', 15, 1);

/**
 * Remove unnecessary header information
 */
function doomovie_remove_header_info() {
    remove_action('wp_head', 'wp_generator');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'rsd_link');
    remove_action('wp_head', 'wp_shortlink_wp_head');
    remove_action('wp_head', 'adjacent_posts_rel_link_wp_head', 10);
    remove_action('wp_head', 'feed_links_extra', 3);
    remove_action('wp_head', 'feed_links', 2);
}
add_action('init', 'doomovie_remove_header_info');

/**
 * Disable XML-RPC
 */
add_filter('xmlrpc_enabled', '__return_false');

/**
 * Remove XML-RPC pingback ping
 */
function doomovie_remove_xmlrpc_pingback_ping($methods) {
    unset($methods['pingback.ping']);
    return $methods;
}
add_filter('xmlrpc_methods', 'doomovie_remove_xmlrpc_pingback_ping');

/**
 * Disable file editing in admin
 */
if (!defined('DISALLOW_FILE_EDIT')) {
    define('DISALLOW_FILE_EDIT', true);
}

/**
 * Hide login errors
 */
function doomovie_hide_login_errors() {
    return __('Something is wrong!', 'doomovie');
}
add_filter('login_errors', 'doomovie_hide_login_errors');

/**
 * Remove WordPress admin bar for non-admins
 */
function doomovie_remove_admin_bar() {
    if (!current_user_can('administrator') && !is_admin()) {
        show_admin_bar(false);
    }
}
add_action('after_setup_theme', 'doomovie_remove_admin_bar');

/**
 * Limit login attempts (basic implementation)
 */
function doomovie_check_login_attempts($user, $username, $password) {
    if (is_wp_error($user)) {
        $ip = $_SERVER['REMOTE_ADDR'];
        $attempts = get_transient('login_attempts_' . $ip);
        
        if ($attempts === false) {
            $attempts = 1;
        } else {
            $attempts++;
        }
        
        set_transient('login_attempts_' . $ip, $attempts, 15 * MINUTE_IN_SECONDS);
        
        if ($attempts >= 5) {
            $user = new WP_Error('too_many_attempts', __('Too many failed login attempts. Please try again later.', 'doomovie'));
        }
    } else {
        // Clear attempts on successful login
        $ip = $_SERVER['REMOTE_ADDR'];
        delete_transient('login_attempts_' . $ip);
    }
    
    return $user;
}
add_filter('authenticate', 'doomovie_check_login_attempts', 30, 3);

/**
 * Add security headers
 */
function doomovie_add_security_headers() {
    if (!is_admin()) {
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
    }
}
add_action('send_headers', 'doomovie_add_security_headers');

/**
 * Sanitize file uploads
 */
function doomovie_sanitize_file_upload($file) {
    $allowed_types = array(
        'jpg', 'jpeg', 'png', 'gif', 'webp', 'svg',
        'mp4', 'webm', 'ogg', 'avi', 'mov',
        'pdf', 'doc', 'docx', 'txt'
    );
    
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if (!in_array($file_extension, $allowed_types)) {
        $file['error'] = __('File type not allowed.', 'doomovie');
    }
    
    return $file;
}
add_filter('wp_handle_upload_prefilter', 'doomovie_sanitize_file_upload');

/**
 * Prevent direct access to PHP files in uploads
 */
function doomovie_prevent_php_upload($mimes) {
    unset($mimes['php']);
    unset($mimes['php3']);
    unset($mimes['php4']);
    unset($mimes['php5']);
    unset($mimes['phtml']);
    return $mimes;
}
add_filter('upload_mimes', 'doomovie_prevent_php_upload');

/**
 * Disable directory browsing
 */
function doomovie_disable_directory_browsing() {
    $htaccess_file = ABSPATH . '.htaccess';
    $rule = "Options -Indexes\n";
    
    if (file_exists($htaccess_file) && is_writable($htaccess_file)) {
        $htaccess_content = file_get_contents($htaccess_file);
        if (strpos($htaccess_content, 'Options -Indexes') === false) {
            file_put_contents($htaccess_file, $rule . $htaccess_content);
        }
    }
}
add_action('admin_init', 'doomovie_disable_directory_browsing');

/**
 * Secure wp-config.php
 */
function doomovie_secure_wp_config() {
    $htaccess_file = ABSPATH . '.htaccess';
    $rule = "<files wp-config.php>\norder allow,deny\ndeny from all\n</files>\n";
    
    if (file_exists($htaccess_file) && is_writable($htaccess_file)) {
        $htaccess_content = file_get_contents($htaccess_file);
        if (strpos($htaccess_content, '<files wp-config.php>') === false) {
            file_put_contents($htaccess_file, $rule . $htaccess_content);
        }
    }
}
add_action('admin_init', 'doomovie_secure_wp_config');

/**
 * Validate and sanitize user input
 */
function doomovie_sanitize_input($input) {
    if (is_array($input)) {
        foreach ($input as $key => $value) {
            $input[$key] = doomovie_sanitize_input($value);
        }
    } else {
        $input = trim($input);
        $input = stripslashes($input);
        $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    }
    return $input;
}

/**
 * Prevent SQL injection in search
 */
function doomovie_secure_search($query) {
    if (!is_admin() && $query->is_search() && $query->is_main_query()) {
        $search_term = get_search_query();
        $search_term = sanitize_text_field($search_term);
        $search_term = esc_sql($search_term);
        $query->set('s', $search_term);
    }
}
add_action('pre_get_posts', 'doomovie_secure_search');

/**
 * Block suspicious user agents
 */
function doomovie_block_suspicious_user_agents() {
    $suspicious_agents = array(
        'libwww-perl',
        'wget',
        'python',
        'nikto',
        'scan',
        'java',
        'winhttp',
        'HTTrack',
        'clshttp',
        'archiver',
        'loader',
        'email',
        'harvest',
        'extract',
        'grab',
        'miner'
    );
    
    $user_agent = strtolower($_SERVER['HTTP_USER_AGENT'] ?? '');
    
    foreach ($suspicious_agents as $agent) {
        if (strpos($user_agent, $agent) !== false) {
            header('HTTP/1.1 403 Forbidden');
            exit('Access Denied');
        }
    }
}
add_action('init', 'doomovie_block_suspicious_user_agents');

/**
 * Rate limiting for AJAX requests
 */
function doomovie_rate_limit_ajax() {
    if (defined('DOING_AJAX') && DOING_AJAX) {
        $ip = $_SERVER['REMOTE_ADDR'];
        $requests = get_transient('ajax_requests_' . $ip);
        
        if ($requests === false) {
            $requests = 1;
        } else {
            $requests++;
        }
        
        set_transient('ajax_requests_' . $ip, $requests, MINUTE_IN_SECONDS);
        
        // Allow 60 requests per minute
        if ($requests > 60) {
            wp_die('Rate limit exceeded');
        }
    }
}
add_action('init', 'doomovie_rate_limit_ajax');

/**
 * Secure cookies
 */
function doomovie_secure_cookies() {
    if (is_ssl()) {
        ini_set('session.cookie_secure', 1);
        ini_set('session.cookie_httponly', 1);
    }
}
add_action('init', 'doomovie_secure_cookies');

/**
 * Content Security Policy
 */
function doomovie_add_csp_header() {
    if (!is_admin()) {
        $csp = "default-src 'self'; ";
        $csp .= "script-src 'self' 'unsafe-inline' 'unsafe-eval' *.googleapis.com *.gstatic.com; ";
        $csp .= "style-src 'self' 'unsafe-inline' *.googleapis.com; ";
        $csp .= "img-src 'self' data: *.gravatar.com *.wp.com; ";
        $csp .= "font-src 'self' *.googleapis.com *.gstatic.com; ";
        $csp .= "connect-src 'self'; ";
        $csp .= "media-src 'self'; ";
        $csp .= "object-src 'none'; ";
        $csp .= "base-uri 'self'; ";
        $csp .= "form-action 'self';";
        
        header("Content-Security-Policy: " . $csp);
    }
}
add_action('send_headers', 'doomovie_add_csp_header');
