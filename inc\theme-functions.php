<?php
/**
 * Theme Support Functions
 * 
 * @package DoMovie
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Custom excerpt length
 */
function doomovie_excerpt_length($length) {
    return 30;
}
add_filter('excerpt_length', 'doomovie_excerpt_length', 999);

/**
 * Custom excerpt more
 */
function doomovie_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'doomovie_excerpt_more');

/**
 * Add body classes
 */
function doomovie_body_classes($classes) {
    // Add class for single movie pages
    if (is_singular('movie')) {
        $classes[] = 'single-movie-page';
    }
    
    // Add class for movie archive pages
    if (is_post_type_archive('movie')) {
        $classes[] = 'movie-archive-page';
    }
    
    // Add class for movie taxonomy pages
    if (is_tax(array('movie_genre', 'movie_year', 'movie_country'))) {
        $classes[] = 'movie-taxonomy-page';
    }
    
    return $classes;
}
add_filter('body_class', 'doomovie_body_classes');
