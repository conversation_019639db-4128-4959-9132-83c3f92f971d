<?php
/**
 * Theme Support Functions
 * 
 * @package DoMovie
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Setup theme supports
 */
function doomovie_theme_support() {
    // Add theme support for post thumbnails
    add_theme_support('post-thumbnails');
    
    // Add theme support for custom logo
    add_theme_support('custom-logo', array(
        'height'      => 100,
        'width'       => 400,
        'flex-height' => true,
        'flex-width'  => true,
    ));
    
    // Add theme support for title tag
    add_theme_support('title-tag');
    
    // Add theme support for HTML5
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
        'style',
        'script',
    ));
    
    // Add theme support for custom header
    add_theme_support('custom-header', array(
        'default-image'      => '',
        'default-text-color' => '000',
        'width'              => 1920,
        'height'             => 200,
        'flex-width'         => true,
        'flex-height'        => true,
    ));
    
    // Add theme support for custom background
    add_theme_support('custom-background', array(
        'default-color' => 'ffffff',
    ));
    
    // Add theme support for selective refresh for widgets
    add_theme_support('customize-selective-refresh-widgets');
    
    // Add theme support for responsive embeds
    add_theme_support('responsive-embeds');
    
    // Add theme support for editor styles
    add_theme_support('editor-styles');
    
    // Add theme support for wide alignment
    add_theme_support('align-wide');
    
    // Add theme support for block editor color palette
    add_theme_support('editor-color-palette', array(
        array(
            'name'  => __('Primary Color', 'doomovie'),
            'slug'  => 'primary',
            'color' => '#007cba',
        ),
        array(
            'name'  => __('Secondary Color', 'doomovie'),
            'slug'  => 'secondary',
            'color' => '#006ba1',
        ),
        array(
            'name'  => __('Dark Gray', 'doomovie'),
            'slug'  => 'dark-gray',
            'color' => '#111',
        ),
        array(
            'name'  => __('Light Gray', 'doomovie'),
            'slug'  => 'light-gray',
            'color' => '#767676',
        ),
        array(
            'name'  => __('White', 'doomovie'),
            'slug'  => 'white',
            'color' => '#FFF',
        ),
    ));
}
add_action('after_setup_theme', 'doomovie_theme_support');

/**
 * Register navigation menus
 */
function doomovie_register_menus() {
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'doomovie'),
        'footer'  => __('Footer Menu', 'doomovie'),
        'mobile'  => __('Mobile Menu', 'doomovie'),
    ));
}
add_action('init', 'doomovie_register_menus');

/**
 * Set content width
 */
function doomovie_content_width() {
    $GLOBALS['content_width'] = apply_filters('doomovie_content_width', 1200);
}
add_action('after_setup_theme', 'doomovie_content_width', 0);

/**
 * Add custom image sizes
 */
function doomovie_custom_image_sizes() {
    // Movie poster sizes
    add_image_size('movie-poster-small', 200, 300, true);
    add_image_size('movie-poster-medium', 300, 450, true);
    add_image_size('movie-poster-large', 400, 600, true);
    
    // Movie thumbnail sizes
    add_image_size('movie-thumb-small', 150, 225, true);
    add_image_size('movie-thumb-medium', 250, 375, true);
    
    // Hero/slider sizes
    add_image_size('hero-slider', 1920, 800, true);
    add_image_size('hero-banner', 1200, 500, true);
    
    // Archive/grid sizes
    add_image_size('archive-thumb', 300, 200, true);
}
add_action('after_setup_theme', 'doomovie_custom_image_sizes');

/**
 * Add editor stylesheet
 */
function doomovie_add_editor_styles() {
    add_editor_style('assets/css/editor-style.css');
}
add_action('admin_init', 'doomovie_add_editor_styles');

/**
 * Custom excerpt length
 */
function doomovie_excerpt_length($length) {
    return 30;
}
add_filter('excerpt_length', 'doomovie_excerpt_length', 999);

/**
 * Custom excerpt more
 */
function doomovie_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'doomovie_excerpt_more');

/**
 * Add body classes
 */
function doomovie_body_classes($classes) {
    // Add class for single movie pages
    if (is_singular('movie')) {
        $classes[] = 'single-movie-page';
    }
    
    // Add class for movie archive pages
    if (is_post_type_archive('movie')) {
        $classes[] = 'movie-archive-page';
    }
    
    // Add class for movie taxonomy pages
    if (is_tax(array('movie_genre', 'movie_year', 'movie_country'))) {
        $classes[] = 'movie-taxonomy-page';
    }
    
    return $classes;
}
add_filter('body_class', 'doomovie_body_classes');

/**
 * Disable WordPress emoji scripts
 */
function doomovie_disable_emojis() {
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('admin_print_scripts', 'print_emoji_detection_script');
    remove_action('wp_print_styles', 'print_emoji_styles');
    remove_action('admin_print_styles', 'print_emoji_styles');
    remove_filter('the_content_feed', 'wp_staticize_emoji');
    remove_filter('comment_text_rss', 'wp_staticize_emoji');
    remove_filter('wp_mail', 'wp_staticize_emoji_for_email');
}
add_action('init', 'doomovie_disable_emojis');

/**
 * Remove WordPress version from head
 */
function doomovie_remove_version() {
    return '';
}
add_filter('the_generator', 'doomovie_remove_version');

/**
 * Clean up wp_head
 */
function doomovie_cleanup_head() {
    remove_action('wp_head', 'wp_generator');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'rsd_link');
    remove_action('wp_head', 'wp_shortlink_wp_head');
    remove_action('wp_head', 'adjacent_posts_rel_link_wp_head', 10);
}
add_action('init', 'doomovie_cleanup_head');
