<?php
/**
 * User Functions for DoMovie Theme
 * 
 * @package DoMovie
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add custom user fields
 */
function doomovie_add_user_fields($user) {
    ?>
    <h3><?php _e('DoMovie Profile Information', 'doomovie'); ?></h3>
    <table class="form-table">
        <tr>
            <th><label for="favorite_genres"><?php _e('Favorite Genres', 'doomovie'); ?></label></th>
            <td>
                <?php
                $favorite_genres = get_user_meta($user->ID, 'favorite_genres', true);
                $genres = get_terms(array(
                    'taxonomy' => 'movie_genre',
                    'hide_empty' => false
                ));
                
                if (!empty($genres)) {
                    foreach ($genres as $genre) {
                        $checked = is_array($favorite_genres) && in_array($genre->term_id, $favorite_genres) ? 'checked' : '';
                        echo '<label><input type="checkbox" name="favorite_genres[]" value="' . $genre->term_id . '" ' . $checked . '> ' . $genre->name . '</label><br>';
                    }
                }
                ?>
            </td>
        </tr>
        <tr>
            <th><label for="watch_later_notifications"><?php _e('Watch Later Notifications', 'doomovie'); ?></label></th>
            <td>
                <input type="checkbox" name="watch_later_notifications" value="1" <?php checked(get_user_meta($user->ID, 'watch_later_notifications', true), 1); ?> />
                <span class="description"><?php _e('Receive notifications for movies in your watch later list', 'doomovie'); ?></span>
            </td>
        </tr>
        <tr>
            <th><label for="new_movie_notifications"><?php _e('New Movie Notifications', 'doomovie'); ?></label></th>
            <td>
                <input type="checkbox" name="new_movie_notifications" value="1" <?php checked(get_user_meta($user->ID, 'new_movie_notifications', true), 1); ?> />
                <span class="description"><?php _e('Receive notifications when new movies are added', 'doomovie'); ?></span>
            </td>
        </tr>
    </table>
    <?php
}
add_action('show_user_profile', 'doomovie_add_user_fields');
add_action('edit_user_profile', 'doomovie_add_user_fields');

/**
 * Save custom user fields
 */
function doomovie_save_user_fields($user_id) {
    if (!current_user_can('edit_user', $user_id)) {
        return false;
    }
    
    // Save favorite genres
    if (isset($_POST['favorite_genres'])) {
        $favorite_genres = array_map('intval', $_POST['favorite_genres']);
        update_user_meta($user_id, 'favorite_genres', $favorite_genres);
    } else {
        delete_user_meta($user_id, 'favorite_genres');
    }
    
    // Save notification preferences
    update_user_meta($user_id, 'watch_later_notifications', isset($_POST['watch_later_notifications']) ? 1 : 0);
    update_user_meta($user_id, 'new_movie_notifications', isset($_POST['new_movie_notifications']) ? 1 : 0);
}
add_action('personal_options_update', 'doomovie_save_user_fields');
add_action('edit_user_profile_update', 'doomovie_save_user_fields');

/**
 * Get user's favorite movies
 */
function doomovie_get_user_favorites($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    if (!$user_id) {
        return array();
    }
    
    $favorites = get_user_meta($user_id, 'favorite_movies', true);
    return is_array($favorites) ? $favorites : array();
}

/**
 * Check if movie is in user's favorites
 */
function doomovie_is_favorite($movie_id, $user_id = null) {
    $favorites = doomovie_get_user_favorites($user_id);
    return in_array($movie_id, $favorites);
}

/**
 * Get user's watch later list
 */
function doomovie_get_user_watch_later($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    if (!$user_id) {
        return array();
    }
    
    $watch_later = get_user_meta($user_id, 'watch_later_movies', true);
    return is_array($watch_later) ? $watch_later : array();
}

/**
 * Add movie to watch later
 */
function doomovie_add_to_watch_later($movie_id, $user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    if (!$user_id) {
        return false;
    }
    
    $watch_later = doomovie_get_user_watch_later($user_id);
    
    if (!in_array($movie_id, $watch_later)) {
        $watch_later[] = $movie_id;
        update_user_meta($user_id, 'watch_later_movies', $watch_later);
        return true;
    }
    
    return false;
}

/**
 * Remove movie from watch later
 */
function doomovie_remove_from_watch_later($movie_id, $user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    if (!$user_id) {
        return false;
    }
    
    $watch_later = doomovie_get_user_watch_later($user_id);
    $key = array_search($movie_id, $watch_later);
    
    if ($key !== false) {
        unset($watch_later[$key]);
        update_user_meta($user_id, 'watch_later_movies', array_values($watch_later));
        return true;
    }
    
    return false;
}

/**
 * Get user's viewing history
 */
function doomovie_get_user_history($user_id = null, $limit = 20) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    if (!$user_id) {
        return array();
    }
    
    $history = get_user_meta($user_id, 'viewing_history', true);
    if (!is_array($history)) {
        return array();
    }
    
    // Sort by timestamp (most recent first)
    uasort($history, function($a, $b) {
        return $b['timestamp'] - $a['timestamp'];
    });
    
    return array_slice($history, 0, $limit, true);
}

/**
 * Add movie to viewing history
 */
function doomovie_add_to_history($movie_id, $user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    if (!$user_id) {
        return false;
    }
    
    $history = get_user_meta($user_id, 'viewing_history', true);
    if (!is_array($history)) {
        $history = array();
    }
    
    // Add or update entry
    $history[$movie_id] = array(
        'movie_id' => $movie_id,
        'timestamp' => time(),
        'date' => current_time('mysql')
    );
    
    // Keep only last 100 entries
    if (count($history) > 100) {
        uasort($history, function($a, $b) {
            return $b['timestamp'] - $a['timestamp'];
        });
        $history = array_slice($history, 0, 100, true);
    }
    
    update_user_meta($user_id, 'viewing_history', $history);
    return true;
}

/**
 * Get user's movie ratings
 */
function doomovie_get_user_ratings($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    if (!$user_id) {
        return array();
    }
    
    $ratings = get_user_meta($user_id, 'movie_ratings', true);
    return is_array($ratings) ? $ratings : array();
}

/**
 * Get user's rating for a specific movie
 */
function doomovie_get_user_movie_rating($movie_id, $user_id = null) {
    $ratings = doomovie_get_user_ratings($user_id);
    return isset($ratings[$movie_id]) ? $ratings[$movie_id] : 0;
}

/**
 * Custom login redirect
 */
function doomovie_login_redirect($redirect_to, $request, $user) {
    if (isset($user->roles) && is_array($user->roles)) {
        if (in_array('administrator', $user->roles)) {
            return admin_url();
        } else {
            return home_url('/my-account/');
        }
    }
    return $redirect_to;
}
add_filter('login_redirect', 'doomovie_login_redirect', 10, 3);

/**
 * Add user account menu items
 */
function doomovie_add_account_menu_items() {
    if (is_user_logged_in()) {
        add_menu_page(
            __('My Account', 'doomovie'),
            __('My Account', 'doomovie'),
            'read',
            'my-account',
            'doomovie_my_account_page',
            'dashicons-admin-users',
            30
        );
        
        add_submenu_page(
            'my-account',
            __('Favorites', 'doomovie'),
            __('Favorites', 'doomovie'),
            'read',
            'my-favorites',
            'doomovie_my_favorites_page'
        );
        
        add_submenu_page(
            'my-account',
            __('Watch Later', 'doomovie'),
            __('Watch Later', 'doomovie'),
            'read',
            'my-watch-later',
            'doomovie_my_watch_later_page'
        );
        
        add_submenu_page(
            'my-account',
            __('History', 'doomovie'),
            __('History', 'doomovie'),
            'read',
            'my-history',
            'doomovie_my_history_page'
        );
    }
}
add_action('admin_menu', 'doomovie_add_account_menu_items');

/**
 * My account page callback
 */
function doomovie_my_account_page() {
    echo '<div class="wrap"><h1>' . __('My Account', 'doomovie') . '</h1>';
    echo '<p>' . __('Welcome to your account dashboard.', 'doomovie') . '</p>';
    echo '</div>';
}

/**
 * My favorites page callback
 */
function doomovie_my_favorites_page() {
    echo '<div class="wrap"><h1>' . __('My Favorites', 'doomovie') . '</h1>';
    // Add favorites list here
    echo '</div>';
}

/**
 * My watch later page callback
 */
function doomovie_my_watch_later_page() {
    echo '<div class="wrap"><h1>' . __('Watch Later', 'doomovie') . '</h1>';
    // Add watch later list here
    echo '</div>';
}

/**
 * My history page callback
 */
function doomovie_my_history_page() {
    echo '<div class="wrap"><h1>' . __('Viewing History', 'doomovie') . '</h1>';
    // Add history list here
    echo '</div>';
}
