<?php
/**
 * The main template file
 * 
 * This is the most generic template file in a WordPress theme
 * and one of the two required files for a theme (the other being style.css).
 * It is used to display a page when nothing more specific matches a query.
 * 
 * @package DoMovie_Theme
 * @version 1.0.0
 */

get_header(); ?>

<main id="main" class="site-main">
    
    <?php if (is_home() || is_front_page()) : ?>

        <!-- Hero Slider Section -->
        <?php if (get_theme_mod('doomovie_show_hero_slider', true)) : ?>
            <section class="hero-section">
                <?php get_template_part('template-parts/movie/movie-slider'); ?>
            </section>
        <?php endif; ?>

        <!-- Featured Movies Section -->
        <section class="featured-movies-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title"><?php _e('Featured Movies', 'doomovie'); ?></h2>
                </div>

                <?php
                $featured_count = get_theme_mod('doomovie_featured_count', 8);
                $featured_movies = new WP_Query(array(
                    'post_type' => 'movie',
                    'posts_per_page' => $featured_count,
                    'meta_query' => array(
                        array(
                            'key' => '_featured_movie',
                            'value' => '1',
                            'compare' => '='
                        )
                    ),
                    'orderby' => 'date',
                    'order' => 'DESC'
                ));

                if (!$featured_movies->have_posts()) {
                    // Fallback to latest movies
                    $featured_movies = new WP_Query(array(
                        'post_type' => 'movie',
                        'posts_per_page' => $featured_count,
                        'orderby' => 'date',
                        'order' => 'DESC'
                    ));
                }

                if ($featured_movies->have_posts()) :
                ?>
                    <div class="movies-grid">
                        <?php
                        while ($featured_movies->have_posts()) :
                            $featured_movies->the_post();
                            get_template_part('template-parts/movie/movie-card');
                        endwhile;
                        wp_reset_postdata();
                        ?>
                    </div>
                <?php else : ?>
                    <p class="no-movies"><?php _e('No featured movies found', 'doomovie'); ?></p>
                <?php endif; ?>
            </div>
        </section>

        <!-- Top 10 Weekly Section -->
        <section class="top-weekly-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title"><?php _e('Top 10 ประจำสัปดาห์', 'doomovie-theme'); ?></h2>
                </div>
                
                <?php
                $popular_movies = get_popular_movies(10);
                if ($popular_movies->have_posts()) :
                ?>
                    <div class="top-movies-list">
                        <?php
                        $counter = 1;
                        while ($popular_movies->have_posts()) :
                            $popular_movies->the_post();
                            ?>
                            <div class="top-movie-item">
                                <span class="rank-number"><?php echo $counter; ?></span>
                                <div class="movie-poster">
                                    <img src="<?php echo get_movie_poster(); ?>" alt="<?php the_title(); ?>" loading="lazy">
                                </div>
                                <div class="movie-info">
                                    <h3 class="movie-title">
                                        <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                    </h3>
                                    <div class="movie-meta">
                                        <span class="year"><?php echo get_movie_year(); ?></span>
                                        <span class="rating"><?php echo get_movie_rating(); ?></span>
                                    </div>
                                </div>
                            </div>
                            <?php
                            $counter++;
                        endwhile;
                        wp_reset_postdata();
                        ?>
                    </div>
                <?php else : ?>
                    <p class="no-movies"><?php _e('ไม่พบหนังยอดนิยม', 'doomovie-theme'); ?></p>
                <?php endif; ?>
            </div>
        </section>

        <!-- Latest Movies Section -->
        <section class="latest-movies-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title"><?php _e('Latest Movies', 'doomovie'); ?></h2>
                    <a href="<?php echo get_post_type_archive_link('movie'); ?>" class="view-all-link">
                        <?php _e('View All', 'doomovie'); ?>
                    </a>
                </div>

                <?php
                $latest_count = get_theme_mod('doomovie_latest_count', 12);
                $latest_movies = new WP_Query(array(
                    'post_type' => 'movie',
                    'posts_per_page' => $latest_count,
                    'orderby' => 'date',
                    'order' => 'DESC'
                ));

                if ($latest_movies->have_posts()) :
                ?>
                    <div class="movies-grid">
                        <?php
                        while ($latest_movies->have_posts()) :
                            $latest_movies->the_post();
                            get_template_part('template-parts/movie/movie-card');
                        endwhile;
                        wp_reset_postdata();
                        ?>
                    </div>
                <?php else : ?>
                    <p class="no-movies"><?php _e('No latest movies found', 'doomovie'); ?></p>
                <?php endif; ?>
            </div>
        </section>

        <!-- Top Recommended Section -->
        <section class="recommended-movies-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title"><?php _e('Top 10 ไม่ควรพลาด', 'doomovie-theme'); ?></h2>
                </div>
                
                <?php
                // Get movies with high IMDB ratings
                $recommended_args = array(
                    'post_type' => 'movie',
                    'posts_per_page' => 10,
                    'meta_key' => 'imdb_rating',
                    'orderby' => 'meta_value_num',
                    'order' => 'DESC',
                    'meta_query' => array(
                        array(
                            'key' => 'imdb_rating',
                            'value' => '7.0',
                            'compare' => '>='
                        )
                    )
                );
                $recommended_movies = new WP_Query($recommended_args);
                
                if ($recommended_movies->have_posts()) :
                ?>
                    <div class="top-movies-list">
                        <?php
                        $counter = 1;
                        while ($recommended_movies->have_posts()) :
                            $recommended_movies->the_post();
                            ?>
                            <div class="top-movie-item">
                                <span class="rank-number"><?php echo $counter; ?></span>
                                <div class="movie-poster">
                                    <img src="<?php echo get_movie_poster(); ?>" alt="<?php the_title(); ?>" loading="lazy">
                                </div>
                                <div class="movie-info">
                                    <h3 class="movie-title">
                                        <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                    </h3>
                                    <div class="movie-meta">
                                        <span class="year"><?php echo get_movie_year(); ?></span>
                                        <span class="imdb-rating">IMDB: <?php echo get_movie_imdb_rating(); ?></span>
                                    </div>
                                </div>
                            </div>
                            <?php
                            $counter++;
                        endwhile;
                        wp_reset_postdata();
                        ?>
                    </div>
                <?php else : ?>
                    <p class="no-movies"><?php _e('ไม่พบหนังแนะนำ', 'doomovie-theme'); ?></p>
                <?php endif; ?>
            </div>
        </section>

        <!-- Genre Sections -->
        <section class="genre-sections">
            <div class="container">
                <?php
                // Get popular genres
                $genres = get_terms(array(
                    'taxonomy' => 'movie_genre',
                    'hide_empty' => true,
                    'number' => 6,
                    'orderby' => 'count',
                    'order' => 'DESC'
                ));

                if ($genres && !is_wp_error($genres)) :
                    foreach ($genres as $genre) :
                        $genre_movies = new WP_Query(array(
                            'post_type' => 'movie',
                            'posts_per_page' => 6,
                            'tax_query' => array(
                                array(
                                    'taxonomy' => 'movie_genre',
                                    'field' => 'term_id',
                                    'terms' => $genre->term_id
                                )
                            ),
                            'orderby' => 'date',
                            'order' => 'DESC'
                        ));

                        if ($genre_movies->have_posts()) :
                ?>
                            <div class="genre-section">
                                <div class="section-header">
                                    <h2 class="section-title"><?php echo $genre->name; ?></h2>
                                    <a href="<?php echo get_term_link($genre); ?>" class="view-all-link">
                                        <?php _e('ดูทั้งหมด', 'doomovie-theme'); ?>
                                    </a>
                                </div>
                                
                                <div class="movie-grid">
                                    <?php
                                    while ($genre_movies->have_posts()) :
                                        $genre_movies->the_post();
                                        get_template_part('template-parts/content/movie-card');
                                    endwhile;
                                    wp_reset_postdata();
                                    ?>
                                </div>
                            </div>
                <?php
                        endif;
                    endforeach;
                endif;
                ?>
            </div>
        </section>

    <?php else : ?>
        
        <!-- Default Blog/Archive Layout -->
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <?php if (have_posts()) : ?>
                        
                        <header class="page-header">
                            <?php
                            the_archive_title('<h1 class="page-title">', '</h1>');
                            the_archive_description('<div class="archive-description">', '</div>');
                            ?>
                        </header>

                        <div class="posts-grid">
                            <?php
                            while (have_posts()) :
                                the_post();
                                get_template_part('template-parts/content/content', get_post_type());
                            endwhile;
                            ?>
                        </div>

                        <?php
                        the_posts_navigation(array(
                            'prev_text' => __('&larr; หน้าก่อนหน้า', 'doomovie-theme'),
                            'next_text' => __('หน้าถัดไป &rarr;', 'doomovie-theme'),
                        ));
                        ?>

                    <?php else : ?>
                        
                        <section class="no-results not-found">
                            <header class="page-header">
                                <h1 class="page-title"><?php _e('ไม่พบเนื้อหา', 'doomovie-theme'); ?></h1>
                            </header>

                            <div class="page-content">
                                <p><?php _e('ขออภัย ไม่พบเนื้อหาที่คุณต้องการ กรุณาลองค้นหาใหม่', 'doomovie-theme'); ?></p>
                                <?php get_search_form(); ?>
                            </div>
                        </section>

                    <?php endif; ?>
                </div>

                <div class="col-lg-4">
                    <?php get_sidebar(); ?>
                </div>
            </div>
        </div>

    <?php endif; ?>

</main>

<?php
get_footer();
