<?php
/**
 * The template for displaying all pages
 *
 * @package DoMovie
 * @since 1.0.0
 */

get_header(); ?>

<main class="main-content page-content">
    <div class="container">
        <?php while (have_posts()) : the_post(); ?>
            <article id="post-<?php the_ID(); ?>" <?php post_class('page-article'); ?>>
                
                <!-- Page Header -->
                <header class="page-header">
                    <h1 class="page-title"><?php the_title(); ?></h1>
                    
                    <?php if (has_excerpt()) : ?>
                        <div class="page-excerpt">
                            <?php the_excerpt(); ?>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Breadcrumbs -->
                    <nav class="breadcrumbs" aria-label="<?php _e('Breadcrumb', 'doomovie'); ?>">
                        <ol class="breadcrumb-list">
                            <li class="breadcrumb-item">
                                <a href="<?php echo esc_url(home_url('/')); ?>">
                                    <i class="fas fa-home"></i>
                                    <?php _e('Home', 'doomovie'); ?>
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">
                                <?php the_title(); ?>
                            </li>
                        </ol>
                    </nav>
                </header>

                <!-- Featured Image -->
                <?php if (has_post_thumbnail()) : ?>
                    <div class="page-featured-image">
                        <?php the_post_thumbnail('large', array('class' => 'featured-image')); ?>
                    </div>
                <?php endif; ?>

                <!-- Page Content -->
                <div class="page-content-wrapper">
                    <div class="page-main-content">
                        <div class="entry-content">
                            <?php
                            the_content();

                            wp_link_pages(array(
                                'before' => '<div class="page-links">' . __('Pages:', 'doomovie'),
                                'after'  => '</div>',
                                'link_before' => '<span class="page-number">',
                                'link_after'  => '</span>',
                            ));
                            ?>
                        </div>

                        <!-- Page Meta -->
                        <div class="page-meta">
                            <div class="page-meta-item">
                                <i class="fas fa-calendar"></i>
                                <span><?php _e('Last updated:', 'doomovie'); ?> <?php echo get_the_modified_date(); ?></span>
                            </div>
                            
                            <?php if (get_the_author()) : ?>
                                <div class="page-meta-item">
                                    <i class="fas fa-user"></i>
                                    <span><?php _e('Author:', 'doomovie'); ?> <?php the_author(); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Social Share -->
                        <div class="social-share">
                            <h3><?php _e('Share this page:', 'doomovie'); ?></h3>
                            <div class="share-buttons">
                                <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(get_permalink()); ?>" 
                                   target="_blank" 
                                   rel="noopener noreferrer"
                                   class="share-btn facebook">
                                    <i class="fab fa-facebook-f"></i>
                                    Facebook
                                </a>
                                
                                <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(get_permalink()); ?>&text=<?php echo urlencode(get_the_title()); ?>" 
                                   target="_blank" 
                                   rel="noopener noreferrer"
                                   class="share-btn twitter">
                                    <i class="fab fa-twitter"></i>
                                    Twitter
                                </a>
                                
                                <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo urlencode(get_permalink()); ?>" 
                                   target="_blank" 
                                   rel="noopener noreferrer"
                                   class="share-btn linkedin">
                                    <i class="fab fa-linkedin-in"></i>
                                    LinkedIn
                                </a>
                                
                                <button class="share-btn copy-link" data-url="<?php echo esc_url(get_permalink()); ?>">
                                    <i class="fas fa-link"></i>
                                    <?php _e('Copy Link', 'doomovie'); ?>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar for specific pages -->
                    <?php if (is_page(array('about', 'contact', 'privacy-policy', 'terms-of-service'))) : ?>
                        <aside class="page-sidebar">
                            <div class="sidebar-widget">
                                <h3><?php _e('Quick Links', 'doomovie'); ?></h3>
                                <ul class="quick-links">
                                    <li><a href="<?php echo esc_url(home_url('/about/')); ?>"><?php _e('About Us', 'doomovie'); ?></a></li>
                                    <li><a href="<?php echo esc_url(home_url('/contact/')); ?>"><?php _e('Contact', 'doomovie'); ?></a></li>
                                    <li><a href="<?php echo esc_url(home_url('/privacy-policy/')); ?>"><?php _e('Privacy Policy', 'doomovie'); ?></a></li>
                                    <li><a href="<?php echo esc_url(home_url('/terms-of-service/')); ?>"><?php _e('Terms of Service', 'doomovie'); ?></a></li>
                                    <li><a href="<?php echo esc_url(home_url('/faq/')); ?>"><?php _e('FAQ', 'doomovie'); ?></a></li>
                                </ul>
                            </div>

                            <div class="sidebar-widget">
                                <h3><?php _e('Contact Info', 'doomovie'); ?></h3>
                                <div class="contact-info">
                                    <?php
                                    $contact_email = get_option('doomovie_contact_email');
                                    if ($contact_email) :
                                    ?>
                                        <div class="contact-item">
                                            <i class="fas fa-envelope"></i>
                                            <a href="mailto:<?php echo esc_attr($contact_email); ?>">
                                                <?php echo esc_html($contact_email); ?>
                                            </a>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Social Media Links -->
                                    <div class="social-links">
                                        <?php
                                        $social_links = array(
                                            'facebook' => get_theme_mod('doomovie_facebook_url'),
                                            'twitter' => get_theme_mod('doomovie_twitter_url'),
                                            'youtube' => get_theme_mod('doomovie_youtube_url'),
                                            'instagram' => get_theme_mod('doomovie_instagram_url')
                                        );

                                        foreach ($social_links as $platform => $url) :
                                            if ($url) :
                                        ?>
                                            <a href="<?php echo esc_url($url); ?>" 
                                               target="_blank" 
                                               rel="noopener noreferrer"
                                               class="social-link <?php echo esc_attr($platform); ?>">
                                                <i class="fab fa-<?php echo esc_attr($platform); ?>"></i>
                                            </a>
                                        <?php 
                                            endif;
                                        endforeach; 
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </aside>
                    <?php endif; ?>
                </div>

                <!-- Comments -->
                <?php
                if (comments_open() || get_comments_number()) :
                    comments_template();
                endif;
                ?>

            </article>
        <?php endwhile; ?>
    </div>
</main>

<style>
.page-content {
    padding: 2rem 0;
}

.page-article {
    max-width: 1000px;
    margin: 0 auto;
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.page-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.page-excerpt {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.breadcrumbs {
    margin-top: 2rem;
}

.breadcrumb-list {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    margin-left: 0.5rem;
    color: var(--text-muted);
}

.breadcrumb-item a {
    color: var(--text-secondary);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.breadcrumb-item a:hover {
    color: var(--primary-color);
}

.breadcrumb-item.active {
    color: var(--text-primary);
}

.page-featured-image {
    margin-bottom: 3rem;
    text-align: center;
}

.featured-image {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.page-content-wrapper {
    display: grid;
    grid-template-columns: 1fr;
    gap: 3rem;
}

.page-content-wrapper.has-sidebar {
    grid-template-columns: 1fr 300px;
}

.entry-content {
    line-height: 1.8;
    margin-bottom: 2rem;
}

.entry-content h2,
.entry-content h3,
.entry-content h4 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.entry-content p {
    margin-bottom: 1.5rem;
}

.entry-content ul,
.entry-content ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.entry-content li {
    margin-bottom: 0.5rem;
}

.page-links {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
    text-align: center;
}

.page-number {
    display: inline-block;
    padding: 0.5rem 1rem;
    margin: 0 0.25rem;
    background-color: var(--secondary-color);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--text-primary);
}

.page-number:hover {
    background-color: var(--primary-color);
    color: white;
}

.page-meta {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    padding: 1rem;
    background-color: var(--secondary-color);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.page-meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.social-share {
    margin-bottom: 3rem;
}

.social-share h3 {
    margin-bottom: 1rem;
    font-size: 1.125rem;
}

.share-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.share-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.share-btn.facebook {
    background-color: #1877f2;
    color: white;
}

.share-btn.twitter {
    background-color: #1da1f2;
    color: white;
}

.share-btn.linkedin {
    background-color: #0077b5;
    color: white;
}

.share-btn.copy-link {
    background-color: var(--secondary-color);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.page-sidebar {
    background-color: var(--secondary-color);
    padding: 2rem;
    border-radius: var(--border-radius);
    height: fit-content;
}

.sidebar-widget {
    margin-bottom: 2rem;
}

.sidebar-widget:last-child {
    margin-bottom: 0;
}

.sidebar-widget h3 {
    margin-bottom: 1rem;
    font-size: 1.125rem;
    color: var(--text-primary);
}

.quick-links {
    list-style: none;
    padding: 0;
}

.quick-links li {
    margin-bottom: 0.5rem;
}

.quick-links a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
}

.quick-links a:hover {
    color: var(--primary-color);
}

.contact-info {
    font-size: 0.875rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

.contact-item a {
    color: var(--text-secondary);
    text-decoration: none;
}

.contact-item a:hover {
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--border-color);
    color: var(--text-secondary);
    border-radius: 50%;
    text-decoration: none;
    transition: var(--transition);
}

.social-link:hover {
    background-color: var(--primary-color);
    color: white;
}

@media (max-width: 768px) {
    .page-title {
        font-size: 2rem;
    }
    
    .breadcrumb-list {
        justify-content: flex-start;
    }
    
    .page-content-wrapper.has-sidebar {
        grid-template-columns: 1fr;
    }
    
    .page-meta {
        flex-direction: column;
        gap: 1rem;
    }
    
    .share-buttons {
        justify-content: center;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Copy link functionality
    const copyLinkBtn = document.querySelector('.copy-link');
    if (copyLinkBtn) {
        copyLinkBtn.addEventListener('click', function() {
            const url = this.dataset.url;
            navigator.clipboard.writeText(url).then(function() {
                // Show success message
                const originalText = copyLinkBtn.innerHTML;
                copyLinkBtn.innerHTML = '<i class="fas fa-check"></i> Copied!';
                setTimeout(() => {
                    copyLinkBtn.innerHTML = originalText;
                }, 2000);
            });
        });
    }
});
</script>

<?php get_footer(); ?>
