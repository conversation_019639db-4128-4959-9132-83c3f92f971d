<?php
/**
 * The template for displaying search results pages
 *
 * @package DoMovie
 * @since 1.0.0
 */

get_header(); ?>

<main class="main-content search-page">
    <div class="container">
        <div class="search-header">
            <h1 class="page-title">
                <?php
                printf(
                    __('Search Results for: %s', 'doomovie'),
                    '<span class="search-term">' . get_search_query() . '</span>'
                );
                ?>
            </h1>
            
            <div class="search-meta">
                <?php
                global $wp_query;
                $total_results = $wp_query->found_posts;
                printf(
                    _n(
                        '%d result found',
                        '%d results found',
                        $total_results,
                        'doomovie'
                    ),
                    $total_results
                );
                ?>
            </div>
        </div>

        <!-- Search Form -->
        <div class="search-form-container">
            <form role="search" method="get" class="search-form" action="<?php echo esc_url(home_url('/')); ?>">
                <div class="search-input-wrapper">
                    <input type="search" 
                           class="search-field" 
                           placeholder="<?php _e('Search movies...', 'doomovie'); ?>" 
                           value="<?php echo get_search_query(); ?>" 
                           name="s" 
                           autocomplete="off">
                    <input type="hidden" name="post_type" value="movie">
                    <button type="submit" class="search-submit">
                        <i class="fas fa-search"></i>
                        <?php _e('Search', 'doomovie'); ?>
                    </button>
                </div>
            </form>
        </div>

        <!-- Search Filters -->
        <div class="search-filters">
            <div class="filter-group">
                <label for="genre-filter"><?php _e('Genre:', 'doomovie'); ?></label>
                <select id="genre-filter" name="genre">
                    <option value=""><?php _e('All Genres', 'doomovie'); ?></option>
                    <?php
                    $genres = get_terms(array(
                        'taxonomy' => 'movie_genre',
                        'hide_empty' => true
                    ));
                    
                    if (!empty($genres) && !is_wp_error($genres)) {
                        foreach ($genres as $genre) {
                            $selected = isset($_GET['genre']) && $_GET['genre'] == $genre->slug ? 'selected' : '';
                            echo '<option value="' . esc_attr($genre->slug) . '" ' . $selected . '>' . esc_html($genre->name) . '</option>';
                        }
                    }
                    ?>
                </select>
            </div>

            <div class="filter-group">
                <label for="year-filter"><?php _e('Year:', 'doomovie'); ?></label>
                <select id="year-filter" name="year">
                    <option value=""><?php _e('All Years', 'doomovie'); ?></option>
                    <?php
                    $years = get_terms(array(
                        'taxonomy' => 'movie_year',
                        'hide_empty' => true,
                        'orderby' => 'name',
                        'order' => 'DESC'
                    ));
                    
                    if (!empty($years) && !is_wp_error($years)) {
                        foreach ($years as $year) {
                            $selected = isset($_GET['year']) && $_GET['year'] == $year->slug ? 'selected' : '';
                            echo '<option value="' . esc_attr($year->slug) . '" ' . $selected . '>' . esc_html($year->name) . '</option>';
                        }
                    }
                    ?>
                </select>
            </div>

            <div class="filter-group">
                <label for="sort-filter"><?php _e('Sort by:', 'doomovie'); ?></label>
                <select id="sort-filter" name="orderby">
                    <option value="relevance" <?php selected(get_query_var('orderby'), 'relevance'); ?>><?php _e('Relevance', 'doomovie'); ?></option>
                    <option value="date" <?php selected(get_query_var('orderby'), 'date'); ?>><?php _e('Latest', 'doomovie'); ?></option>
                    <option value="title" <?php selected(get_query_var('orderby'), 'title'); ?>><?php _e('Title A-Z', 'doomovie'); ?></option>
                    <option value="rating" <?php selected(get_query_var('orderby'), 'rating'); ?>><?php _e('Rating', 'doomovie'); ?></option>
                </select>
            </div>

            <button type="button" class="filter-apply-btn" id="applyFilters">
                <?php _e('Apply Filters', 'doomovie'); ?>
            </button>
        </div>

        <!-- Search Results -->
        <div class="search-results-container">
            <?php if (have_posts()) : ?>
                <div class="movies-grid" id="searchResults">
                    <?php while (have_posts()) : the_post(); ?>
                        <?php get_template_part('template-parts/movie/movie-card'); ?>
                    <?php endwhile; ?>
                </div>

                <!-- Pagination -->
                <div class="pagination-wrapper">
                    <?php
                    the_posts_pagination(array(
                        'mid_size' => 2,
                        'prev_text' => '<i class="fas fa-chevron-left"></i> ' . __('Previous', 'doomovie'),
                        'next_text' => __('Next', 'doomovie') . ' <i class="fas fa-chevron-right"></i>',
                        'screen_reader_text' => __('Posts navigation', 'doomovie'),
                    ));
                    ?>
                </div>

                <!-- Load More Button (Alternative to pagination) -->
                <?php if ($wp_query->max_num_pages > 1) : ?>
                    <div class="load-more-wrapper" style="display: none;">
                        <button class="btn btn-primary load-more-btn" 
                                data-page="1" 
                                data-max-pages="<?php echo $wp_query->max_num_pages; ?>"
                                data-search-query="<?php echo esc_attr(get_search_query()); ?>">
                            <i class="fas fa-plus"></i>
                            <?php _e('Load More Movies', 'doomovie'); ?>
                        </button>
                    </div>
                <?php endif; ?>

            <?php else : ?>
                <div class="no-results">
                    <div class="no-results-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h2><?php _e('No movies found', 'doomovie'); ?></h2>
                    <p><?php _e('Sorry, but nothing matched your search terms. Please try again with different keywords.', 'doomovie'); ?></p>
                    
                    <!-- Suggestions -->
                    <div class="search-suggestions">
                        <h3><?php _e('Search Suggestions:', 'doomovie'); ?></h3>
                        <ul>
                            <li><?php _e('Check your spelling', 'doomovie'); ?></li>
                            <li><?php _e('Try different keywords', 'doomovie'); ?></li>
                            <li><?php _e('Try more general keywords', 'doomovie'); ?></li>
                            <li><?php _e('Try fewer keywords', 'doomovie'); ?></li>
                        </ul>
                    </div>

                    <!-- Popular Movies -->
                    <?php
                    $popular_movies = new WP_Query(array(
                        'post_type' => 'movie',
                        'posts_per_page' => 8,
                        'meta_key' => '_view_count',
                        'orderby' => 'meta_value_num',
                        'order' => 'DESC'
                    ));
                    
                    if ($popular_movies->have_posts()) :
                    ?>
                        <div class="popular-movies-section">
                            <h3><?php _e('Popular Movies', 'doomovie'); ?></h3>
                            <div class="movies-grid movies-grid-small">
                                <?php while ($popular_movies->have_posts()) : $popular_movies->the_post(); ?>
                                    <?php get_template_part('template-parts/movie/movie-card'); ?>
                                <?php endwhile; ?>
                            </div>
                        </div>
                    <?php 
                    endif;
                    wp_reset_postdata();
                    ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Filter functionality
    const applyFiltersBtn = document.getElementById('applyFilters');
    const genreFilter = document.getElementById('genre-filter');
    const yearFilter = document.getElementById('year-filter');
    const sortFilter = document.getElementById('sort-filter');
    
    if (applyFiltersBtn) {
        applyFiltersBtn.addEventListener('click', function() {
            const currentUrl = new URL(window.location);
            const searchParams = currentUrl.searchParams;
            
            // Update URL parameters
            if (genreFilter.value) {
                searchParams.set('genre', genreFilter.value);
            } else {
                searchParams.delete('genre');
            }
            
            if (yearFilter.value) {
                searchParams.set('year', yearFilter.value);
            } else {
                searchParams.delete('year');
            }
            
            if (sortFilter.value && sortFilter.value !== 'relevance') {
                searchParams.set('orderby', sortFilter.value);
            } else {
                searchParams.delete('orderby');
            }
            
            // Redirect to filtered results
            window.location.href = currentUrl.toString();
        });
    }
    
    // Load more functionality
    const loadMoreBtn = document.querySelector('.load-more-btn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            const currentPage = parseInt(this.dataset.page);
            const maxPages = parseInt(this.dataset.maxPages);
            const searchQuery = this.dataset.searchQuery;
            
            if (currentPage < maxPages) {
                // Implement AJAX load more here
                loadMoreSearchResults(currentPage + 1, searchQuery);
            }
        });
    }
});

function loadMoreSearchResults(page, searchQuery) {
    // AJAX implementation for loading more search results
    // This would use the existing AJAX handler from main.js
}
</script>

<?php get_footer(); ?>
