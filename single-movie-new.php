<?php
/**
 * Modern Single Movie Template
 * Enhanced movie detail page with tabs and modern design
 *
 * @package DoMovie
 */

get_header();

while (have_posts()) :
    the_post();
    
    $movie_id = get_the_ID();
    $poster_url = get_post_meta($movie_id, '_movie_poster', true);
    $year = get_post_meta($movie_id, '_movie_year', true);
    $rating = get_post_meta($movie_id, '_movie_rating', true);
    $quality = get_post_meta($movie_id, '_movie_quality', true);
    $imdb_rating = get_post_meta($movie_id, '_movie_imdb_rating', true);
    $duration = get_post_meta($movie_id, '_movie_duration', true);
    $genres = wp_get_post_terms($movie_id, 'movie_genre');
    $categories = wp_get_post_terms($movie_id, 'movie_category');
    
    // Increment view count
    $views = get_post_meta($movie_id, '_movie_views', true);
    $views = $views ? intval($views) + 1 : 1;
    update_post_meta($movie_id, '_movie_views', $views);
?>

<main id="primary" class="site-main single-movie-modern">
    
    <!-- Movie Hero Section -->
    <section class="movie-hero-modern" style="background-image: url('<?php echo esc_url($poster_url); ?>')">
        <div class="movie-hero-overlay"></div>
        <div class="container">
            <div class="movie-hero-content">
                
                <!-- Movie Poster -->
                <div class="movie-poster-large">
                    <?php if ($poster_url) : ?>
                        <img src="<?php echo esc_url($poster_url); ?>" 
                             alt="<?php the_title_attribute(); ?>" 
                             class="poster-image">
                    <?php endif; ?>
                    
                    <!-- Play Button -->
                    <button class="play-movie-btn">
                        <i class="fas fa-play"></i>
                        <span><?php _e('ดูหนัง', 'doomovie'); ?></span>
                    </button>
                </div>

                <!-- Movie Info -->
                <div class="movie-info-detailed">
                    
                    <!-- Movie Badges -->
                    <div class="movie-badges">
                        <?php if ($quality) : ?>
                            <span class="badge quality-badge"><?php echo esc_html($quality); ?></span>
                        <?php endif; ?>
                        
                        <?php if ($year) : ?>
                            <span class="badge year-badge"><?php echo esc_html($year); ?></span>
                        <?php endif; ?>
                        
                        <?php if ($imdb_rating) : ?>
                            <span class="badge imdb-badge">IMDb <?php echo esc_html($imdb_rating); ?></span>
                        <?php endif; ?>
                        
                        <?php if ($rating) : ?>
                            <span class="badge rating-badge"><?php echo esc_html($rating); ?></span>
                        <?php endif; ?>
                    </div>

                    <!-- Movie Title -->
                    <h1 class="movie-title-large"><?php the_title(); ?></h1>

                    <!-- Movie Meta -->
                    <div class="movie-meta-detailed">
                        <?php if ($year) : ?>
                            <div class="meta-item">
                                <i class="fas fa-calendar"></i>
                                <span><?php echo esc_html($year); ?></span>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($duration) : ?>
                            <div class="meta-item">
                                <i class="fas fa-clock"></i>
                                <span><?php echo esc_html($duration); ?></span>
                            </div>
                        <?php endif; ?>
                        
                        <div class="meta-item">
                            <i class="fas fa-eye"></i>
                            <span><?php echo number_format($views); ?> <?php _e('ครั้ง', 'doomovie'); ?></span>
                        </div>
                    </div>

                    <!-- Movie Genres -->
                    <?php if (!empty($genres)) : ?>
                        <div class="movie-genres-detailed">
                            <strong><?php _e('แนวหนัง:', 'doomovie'); ?></strong>
                            <div class="genres-list">
                                <?php foreach ($genres as $genre) : ?>
                                    <a href="<?php echo get_term_link($genre); ?>" class="genre-link">
                                        <?php echo esc_html($genre->name); ?>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Movie Description -->
                    <div class="movie-description">
                        <?php the_content(); ?>
                    </div>

                    <!-- Action Buttons -->
                    <div class="movie-actions-detailed">
                        <button class="btn btn-primary btn-large btn-watch">
                            <i class="fas fa-play"></i>
                            <?php _e('ดูหนังออนไลน์', 'doomovie'); ?>
                        </button>
                        
                        <button class="btn btn-secondary btn-large btn-favorite" data-movie-id="<?php echo esc_attr($movie_id); ?>">
                            <i class="far fa-heart"></i>
                            <?php _e('เพิ่มในรายการโปรด', 'doomovie'); ?>
                        </button>
                        
                        <button class="btn btn-outline btn-large btn-share">
                            <i class="fas fa-share-alt"></i>
                            <?php _e('แชร์', 'doomovie'); ?>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Movies -->
    <section class="related-movies">
        <div class="container">
            <h2 class="section-title"><?php _e('หนังที่เกี่ยวข้อง', 'doomovie'); ?></h2>
            
            <?php
            // Get related movies from same genres
            $related_movies = new WP_Query(array(
                'post_type' => 'movie',
                'posts_per_page' => 6,
                'post__not_in' => array($movie_id),
                'tax_query' => array(
                    array(
                        'taxonomy' => 'movie_genre',
                        'field'    => 'term_id',
                        'terms'    => wp_list_pluck($genres, 'term_id'),
                    ),
                ),
                'orderby' => 'rand'
            ));
            
            if ($related_movies->have_posts()) :
            ?>
                <div class="movies-grid">
                    <?php
                    while ($related_movies->have_posts()) :
                        $related_movies->the_post();
                        get_template_part('template-parts/movie/movie-card-simple');
                    endwhile;
                    wp_reset_postdata();
                    ?>
                </div>
            <?php endif; ?>
        </div>
    </section>

</main>

<style>
/* Modern Single Movie Styles */
.movie-hero-modern {
    position: relative;
    min-height: 80vh;
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    color: white;
}

.movie-hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(0,0,0,0.9) 0%,
        rgba(0,0,0,0.7) 50%,
        rgba(0,0,0,0.9) 100%
    );
}

.movie-hero-content {
    position: relative;
    z-index: 2;
    display: flex;
    gap: 4rem;
    align-items: flex-start;
}

.movie-poster-large {
    position: relative;
    flex-shrink: 0;
}

.movie-poster-large .poster-image {
    width: 350px;
    height: 525px;
    object-fit: cover;
    border-radius: var(--border-radius);
    box-shadow: 0 20px 60px rgba(0,0,0,0.8);
}

.play-movie-btn {
    position: absolute;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradient-primary);
    border: none;
    border-radius: 25px;
    color: white;
    padding: 0.75rem 1.5rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
}

.play-movie-btn:hover {
    transform: translateX(-50%) translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 107, 53, 0.6);
}

.movie-info-detailed {
    flex: 1;
}

.movie-badges {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.quality-badge {
    background: var(--gradient-primary);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
}

.year-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.imdb-badge {
    background: var(--accent-color);
    color: #000;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
}

.rating-badge {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.movie-title-large {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 2rem;
    line-height: 1.1;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    background: linear-gradient(135deg, #ffffff 0%, var(--primary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.movie-meta-detailed {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
}

.meta-item i {
    color: var(--primary-color);
}

.movie-genres-detailed {
    margin-bottom: 2rem;
}

.movie-genres-detailed strong {
    color: var(--primary-color);
    font-size: 1.1rem;
}

.genres-list {
    display: flex;
    gap: 1rem;
    margin-top: 0.5rem;
    flex-wrap: wrap;
}

.genre-link {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 15px;
    text-decoration: none;
    transition: var(--transition);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.genre-link:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
}

.movie-description {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 3rem;
    color: rgba(255, 255, 255, 0.9);
}

.movie-actions-detailed {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.related-movies {
    padding: 4rem 0;
    background: var(--dark-bg);
}

/* Responsive */
@media (max-width: 992px) {
    .movie-hero-content {
        flex-direction: column;
        text-align: center;
        gap: 2rem;
    }
    
    .movie-poster-large .poster-image {
        width: 280px;
        height: 420px;
    }
    
    .movie-title-large {
        font-size: 2.5rem;
    }
}

@media (max-width: 768px) {
    .movie-actions-detailed {
        flex-direction: column;
    }
    
    .btn-large {
        width: 100%;
        justify-content: center;
    }
    
    .movie-meta-detailed {
        justify-content: center;
    }
    
    .genres-list {
        justify-content: center;
    }
}
</style>

<?php
endwhile;
get_footer();
?>
