<?php
/**
 * Template for displaying movie category archives
 *
 * @package DoMovie
 */

get_header();

$current_term = get_queried_object();
$category_slug = $current_term->slug;
$category_name = $current_term->name;
$category_description = $current_term->description;

// Category specific configurations
$category_configs = array(
    'new-movies' => array(
        'icon' => 'fas fa-star',
        'color' => '#10b981',
        'bg_gradient' => 'linear-gradient(135deg, #10b981 0%, #059669 100%)'
    ),
    'series' => array(
        'icon' => 'fas fa-tv',
        'color' => '#8b5cf6',
        'bg_gradient' => 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)'
    ),
    'thai-movies' => array(
        'icon' => 'fas fa-flag',
        'color' => '#f59e0b',
        'bg_gradient' => 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)'
    ),
    'hero-movies' => array(
        'icon' => 'fas fa-mask',
        'color' => '#ef4444',
        'bg_gradient' => 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)'
    )
);

$config = isset($category_configs[$category_slug]) ? $category_configs[$category_slug] : array(
    'icon' => 'fas fa-film',
    'color' => '#ff6b35',
    'bg_gradient' => 'linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%)'
);
?>

<main id="primary" class="site-main category-archive">
    
    <!-- Category Hero Section -->
    <section class="category-hero" style="background: <?php echo $config['bg_gradient']; ?>">
        <div class="category-hero-overlay"></div>
        <div class="container">
            <div class="category-hero-content">
                <div class="category-icon">
                    <i class="<?php echo esc_attr($config['icon']); ?>"></i>
                </div>
                <h1 class="category-title"><?php echo esc_html($category_name); ?></h1>
                <?php if ($category_description) : ?>
                    <p class="category-description"><?php echo esc_html($category_description); ?></p>
                <?php endif; ?>
                <div class="category-stats">
                    <span class="movie-count">
                        <i class="fas fa-film"></i>
                        <?php printf(_n('%d หนัง', '%d หนัง', $wp_query->found_posts, 'doomovie'), $wp_query->found_posts); ?>
                    </span>
                </div>
            </div>
        </div>
        
        <!-- Animated Background -->
        <div class="hero-bg-animation">
            <div class="floating-shape shape-1"></div>
            <div class="floating-shape shape-2"></div>
            <div class="floating-shape shape-3"></div>
        </div>
    </section>

    <!-- Filter & Sort Section -->
    <section class="category-filters">
        <div class="container">
            <div class="filters-wrapper">
                <div class="filter-group">
                    <label for="sort-by"><?php _e('เรียงตาม:', 'doomovie'); ?></label>
                    <select id="sort-by" class="filter-select">
                        <option value="date"><?php _e('วันที่เพิ่ม', 'doomovie'); ?></option>
                        <option value="title"><?php _e('ชื่อเรื่อง', 'doomovie'); ?></option>
                        <option value="year"><?php _e('ปีที่ออกฉาย', 'doomovie'); ?></option>
                        <option value="rating"><?php _e('คะแนน IMDB', 'doomovie'); ?></option>
                        <option value="views"><?php _e('ยอดชม', 'doomovie'); ?></option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="filter-year"><?php _e('ปี:', 'doomovie'); ?></label>
                    <select id="filter-year" class="filter-select">
                        <option value=""><?php _e('ทุกปี', 'doomovie'); ?></option>
                        <option value="2024">2024</option>
                        <option value="2023">2023</option>
                        <option value="2022">2022</option>
                        <option value="2021">2021</option>
                        <option value="2020">2020</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="filter-quality"><?php _e('คุณภาพ:', 'doomovie'); ?></label>
                    <select id="filter-quality" class="filter-select">
                        <option value=""><?php _e('ทุกคุณภาพ', 'doomovie'); ?></option>
                        <option value="4K">4K</option>
                        <option value="HD">HD</option>
                        <option value="CAM">CAM</option>
                    </select>
                </div>
                
                <div class="view-toggle">
                    <button class="view-btn active" data-view="grid">
                        <i class="fas fa-th"></i>
                    </button>
                    <button class="view-btn" data-view="list">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Movies Grid Section -->
    <section class="category-movies">
        <div class="container">
            <?php if (have_posts()) : ?>
                <div class="movies-grid stagger-animation" id="movies-container">
                    <?php
                    while (have_posts()) :
                        the_post();
                        get_template_part('template-parts/movie/movie-card-simple');
                    endwhile;
                    ?>
                </div>

                <!-- Pagination -->
                <div class="pagination-wrapper">
                    <?php
                    $pagination = paginate_links(array(
                        'total' => $wp_query->max_num_pages,
                        'current' => max(1, get_query_var('paged')),
                        'format' => '?paged=%#%',
                        'show_all' => false,
                        'end_size' => 1,
                        'mid_size' => 2,
                        'prev_next' => true,
                        'prev_text' => '<i class="fas fa-chevron-left"></i>',
                        'next_text' => '<i class="fas fa-chevron-right"></i>',
                        'add_args' => false,
                        'add_fragment' => '',
                        'type' => 'list'
                    ));
                    
                    if ($pagination) {
                        echo '<nav class="pagination-nav">' . $pagination . '</nav>';
                    }
                    ?>
                </div>

            <?php else : ?>
                <div class="no-movies-found">
                    <div class="no-movies-icon">
                        <i class="fas fa-film"></i>
                    </div>
                    <h3><?php _e('ไม่พบหนังในหมวดนี้', 'doomovie'); ?></h3>
                    <p><?php _e('ขออภัย ยังไม่มีหนังในหมวดหมู่นี้ในขณะนี้', 'doomovie'); ?></p>
                    <a href="<?php echo esc_url(home_url('/')); ?>" class="btn btn-primary">
                        <i class="fas fa-home"></i>
                        <?php _e('กลับหน้าหลัก', 'doomovie'); ?>
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </section>

</main>

<style>
/* Category Archive Styles */
.category-hero {
    position: relative;
    padding: 6rem 0 4rem;
    color: white;
    overflow: hidden;
}

.category-hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.category-hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.category-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin: 0 auto 2rem;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.category-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.category-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.category-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
}

.movie-count {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
    font-weight: 600;
}

.hero-bg-animation {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
}

.floating-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 100px;
    height: 100px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 80px;
    height: 80px;
    bottom: 30%;
    left: 70%;
    animation-delay: 4s;
}

/* Filters */
.category-filters {
    background: var(--card-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 2rem 0;
}

.filters-wrapper {
    display: flex;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 600;
    color: var(--text-secondary);
    white-space: nowrap;
}

.filter-select {
    background: var(--darker-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-small);
    color: var(--text-primary);
    padding: 0.5rem 1rem;
    min-width: 120px;
}

.view-toggle {
    margin-left: auto;
    display: flex;
    gap: 0.5rem;
}

.view-btn {
    width: 40px;
    height: 40px;
    background: var(--darker-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-small);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
}

.view-btn.active,
.view-btn:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* Movies Section */
.category-movies {
    padding: 4rem 0;
}

/* Pagination */
.pagination-wrapper {
    margin-top: 4rem;
    text-align: center;
}

.pagination-nav ul {
    display: inline-flex;
    list-style: none;
    gap: 0.5rem;
    margin: 0;
    padding: 0;
}

.pagination-nav li a,
.pagination-nav li span {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-small);
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition);
}

.pagination-nav li a:hover,
.pagination-nav li.current span {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* No Movies Found */
.no-movies-found {
    text-align: center;
    padding: 4rem 2rem;
}

.no-movies-icon {
    font-size: 4rem;
    color: var(--text-muted);
    margin-bottom: 2rem;
}

.no-movies-found h3 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.no-movies-found p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

/* Responsive */
@media (max-width: 768px) {
    .category-title {
        font-size: 2.5rem;
    }
    
    .filters-wrapper {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
    
    .view-toggle {
        margin-left: 0;
        justify-content: center;
    }
    
    .category-stats {
        flex-direction: column;
        gap: 1rem;
    }
}
</style>

<?php get_footer(); ?>
