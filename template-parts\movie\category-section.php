<?php
/**
 * Movie Category Section Template
 * Display movies by category with modern graphics
 *
 * @package DoMovie
 */

$category = isset($args['category']) ? $args['category'] : '';
$title = isset($args['title']) ? $args['title'] : '';
$icon = isset($args['icon']) ? $args['icon'] : 'fas fa-film';
$description = isset($args['description']) ? $args['description'] : '';
$limit = isset($args['limit']) ? $args['limit'] : 12;
$show_view_all = isset($args['show_view_all']) ? $args['show_view_all'] : true;
$grid_class = isset($args['grid_class']) ? $args['grid_class'] : 'movies-grid';

// Query movies based on category
$movies_query = array(
    'post_type' => 'movie',
    'posts_per_page' => $limit,
    'orderby' => 'date',
    'order' => 'DESC',
    'post_status' => 'publish'
);

// Add category filter if specified
if (!empty($category)) {
    $movies_query['tax_query'] = array(
        array(
            'taxonomy' => 'movie_category',
            'field'    => 'slug',
            'terms'    => $category,
        ),
    );
}

$movies = new WP_Query($movies_query);

if ($movies->have_posts()) :
?>
<section class="category-section <?php echo esc_attr($category); ?>-section">
    <div class="container">
        
        <!-- Section Header with Modern Graphics -->
        <div class="section-header-modern">
            <div class="section-title-wrapper">
                <div class="section-icon">
                    <i class="<?php echo esc_attr($icon); ?>"></i>
                </div>
                <div class="section-text">
                    <h2 class="section-title"><?php echo esc_html($title); ?></h2>
                    <?php if ($description) : ?>
                        <p class="section-description"><?php echo esc_html($description); ?></p>
                    <?php endif; ?>
                </div>
            </div>
            
            <?php if ($show_view_all && !empty($category)) : ?>
                <div class="section-actions">
                    <a href="<?php echo esc_url(get_term_link($category, 'movie_category')); ?>"
                       class="view-all-btn-modern">
                        <span class="btn-text"><?php _e('ดูทั้งหมด', 'doomovie'); ?></span>
                        <div class="btn-icon">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                        <div class="btn-glow"></div>
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <!-- Movies Grid -->
        <div class="<?php echo esc_attr($grid_class); ?> stagger-animation reveal-on-scroll">
            <?php
            while ($movies->have_posts()) :
                $movies->the_post();
                get_template_part('template-parts/movie/movie-card-simple');
            endwhile;
            wp_reset_postdata();
            ?>
        </div>

        <!-- Load More Button -->
        <?php if ($movies->found_posts > $limit) : ?>
            <div class="load-more-wrapper">
                <button class="load-more-btn btn btn-outline" 
                        data-category="<?php echo esc_attr($category); ?>"
                        data-page="1"
                        data-max-pages="<?php echo ceil($movies->found_posts / $limit); ?>"
                        data-posts-per-page="<?php echo esc_attr($limit); ?>">
                    <i class="fas fa-plus"></i>
                    <?php _e('Load More Movies', 'doomovie'); ?>
                </button>
            </div>
        <?php endif; ?>
    </div>

    <!-- Background Graphics -->
    <div class="section-bg-graphics">
        <div class="bg-shape bg-shape-1"></div>
        <div class="bg-shape bg-shape-2"></div>
        <div class="bg-shape bg-shape-3"></div>
    </div>
</section>

<?php endif; ?>

<style>
/* Modern Category Section Styles */
.category-section {
    position: relative;
    padding: 4rem 0;
    overflow: hidden;
}

.category-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(255, 107, 53, 0.02) 0%,
        transparent 50%,
        rgba(0, 212, 255, 0.02) 100%
    );
    pointer-events: none;
}

.section-header-modern {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 3rem;
    position: relative;
    z-index: 2;
}

.section-title-wrapper {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.section-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    box-shadow: 0 8px 32px rgba(255, 107, 53, 0.4);
    animation: pulse 2s infinite;
}

.section-text h2 {
    margin: 0 0 0.5rem 0;
    font-size: 2.5rem;
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-description {
    margin: 0;
    color: var(--text-muted);
    font-size: 1rem;
    line-height: 1.5;
}

/* Modern View All Button */
.view-all-btn-modern {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border: 2px solid transparent;
    border-radius: 50px;
    color: white;
    text-decoration: none;
    font-weight: 700;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.320, 1);
    box-shadow: 0 10px 40px rgba(255, 107, 53, 0.3);
}

.view-all-btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.view-all-btn-modern:hover::before {
    left: 100%;
}

.view-all-btn-modern:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 20px 60px rgba(255, 107, 53, 0.5);
    border-color: rgba(255, 255, 255, 0.3);
}

.btn-text {
    position: relative;
    z-index: 2;
    transition: var(--transition);
}

.btn-icon {
    position: relative;
    z-index: 2;
    width: 24px;
    height: 24px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.4s ease;
}

.view-all-btn-modern:hover .btn-icon {
    background: rgba(255, 255, 255, 0.3);
    transform: translateX(5px) rotate(45deg);
}

.btn-glow {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: var(--gradient-primary);
    border-radius: 50px;
    z-index: -1;
    filter: blur(15px);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.view-all-btn-modern:hover .btn-glow {
    opacity: 0.8;
}

.load-more-wrapper {
    text-align: center;
    margin-top: 3rem;
}

.load-more-btn {
    padding: 1rem 2rem;
    font-size: 1rem;
    border-radius: 25px;
    position: relative;
    overflow: hidden;
}

.load-more-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: left 0.5s ease;
    z-index: -1;
}

.load-more-btn:hover::before {
    left: 0;
}

/* Background Graphics */
.section-bg-graphics {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
}

.bg-shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(
        45deg,
        rgba(255, 107, 53, 0.1),
        rgba(0, 212, 255, 0.1)
    );
    filter: blur(40px);
    animation: float 6s ease-in-out infinite;
}

.bg-shape-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    right: 10%;
    animation-delay: 0s;
}

.bg-shape-2 {
    width: 150px;
    height: 150px;
    bottom: 20%;
    left: 15%;
    animation-delay: 2s;
}

.bg-shape-3 {
    width: 100px;
    height: 100px;
    top: 60%;
    right: 30%;
    animation-delay: 4s;
}

/* Category Specific Styles */
.new-movies-section .section-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    box-shadow: 0 8px 32px rgba(16, 185, 129, 0.4);
}

.series-section .section-icon {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    box-shadow: 0 8px 32px rgba(139, 92, 246, 0.4);
}

.thai-movies-section .section-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 8px 32px rgba(245, 158, 11, 0.4);
}

.hero-movies-section .section-icon {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    box-shadow: 0 8px 32px rgba(239, 68, 68, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
    .section-header-modern {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }
    
    .section-title-wrapper {
        flex-direction: column;
        gap: 1rem;
    }
    
    .section-text h2 {
        font-size: 2rem;
    }
    
    .section-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
}
</style>
