<?php
/**
 * Simple Movie Card Template - ezmovie.me style
 *
 * @package DoMovie
 */

$movie_id = get_the_ID();
$poster_url = get_post_meta($movie_id, '_movie_poster', true);
$year = get_post_meta($movie_id, '_movie_year', true);
$rating = get_post_meta($movie_id, '_movie_rating', true);
$quality = get_post_meta($movie_id, '_movie_quality', true);
$imdb_rating = get_post_meta($movie_id, '_movie_imdb_rating', true);
$duration = get_post_meta($movie_id, '_movie_duration', true);
$genres = wp_get_post_terms($movie_id, 'movie_genre');
$is_favorite = is_user_logged_in() ? get_user_meta(get_current_user_id(), '_favorite_movies', true) : false;
$is_favorite = is_array($is_favorite) && in_array($movie_id, $is_favorite);

// Fallback poster
if (!$poster_url) {
    $poster_url = get_template_directory_uri() . '/assets/images/no-poster.svg';
}
?>

<article class="movie-card neon-glow interactive-element ripple" data-movie-id="<?php echo esc_attr($movie_id); ?>">
    <div class="movie-poster">
        <a href="<?php the_permalink(); ?>">
            <img src="<?php echo esc_url($poster_url); ?>" 
                 alt="<?php the_title_attribute(); ?>" 
                 class="poster-image"
                 loading="lazy">
        </a>
        
        <!-- Movie Actions -->
        <div class="movie-actions">
            <button class="btn-favorite <?php echo $is_favorite ? 'active' : ''; ?>" 
                    data-movie-id="<?php echo esc_attr($movie_id); ?>"
                    data-tooltip="<?php echo $is_favorite ? __('Remove from favorites', 'doomovie') : __('Add to favorites', 'doomovie'); ?>">
                <i class="<?php echo $is_favorite ? 'fas' : 'far'; ?> fa-heart"></i>
            </button>
            
            <button class="btn-watch-later" 
                    data-movie-id="<?php echo esc_attr($movie_id); ?>"
                    data-tooltip="<?php _e('Add to watch later', 'doomovie'); ?>">
                <i class="far fa-bookmark"></i>
            </button>
        </div>
        
        <!-- Movie Overlay -->
        <div class="movie-overlay">
            <a href="<?php the_permalink(); ?>" class="play-button liquid-button pulse-animation" aria-label="<?php _e('Watch Movie', 'doomovie'); ?>">
                <i class="fas fa-play"></i>
            </a>
        </div>
        
        <!-- Movie Info Badges -->
        <div class="movie-info">
            <?php if ($quality) : ?>
                <span class="movie-quality"><?php echo esc_html($quality); ?></span>
            <?php endif; ?>
            
            <?php if ($year) : ?>
                <span class="movie-year"><?php echo esc_html($year); ?></span>
            <?php endif; ?>
            
            <?php if ($imdb_rating) : ?>
                <span class="imdb-rating">IMDb <?php echo esc_html($imdb_rating); ?></span>
            <?php endif; ?>
        </div>
        
        <!-- Movie Details (appears on hover) -->
        <div class="movie-details">
            <h3 class="movie-title">
                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
            </h3>
            
            <div class="movie-meta">
                <?php if ($year) : ?>
                    <span class="movie-year"><?php echo esc_html($year); ?></span>
                <?php endif; ?>
                
                <?php if ($rating) : ?>
                    <span class="movie-rating"><?php echo esc_html($rating); ?></span>
                <?php endif; ?>
                
                <?php if ($duration) : ?>
                    <span class="movie-duration"><?php echo esc_html($duration); ?></span>
                <?php else : ?>
                    <span class="movie-duration">120 min</span>
                <?php endif; ?>
            </div>
            
            <?php if (!empty($genres) && count($genres) > 0) : ?>
                <div class="movie-genres">
                    <?php 
                    $genre_count = 0;
                    foreach ($genres as $genre) : 
                        if ($genre_count >= 2) break; // Show only first 2 genres
                    ?>
                        <a href="<?php echo get_term_link($genre); ?>" class="genre-tag">
                            <?php echo esc_html($genre->name); ?>
                        </a>
                    <?php 
                        $genre_count++;
                    endforeach; 
                    ?>
                </div>
            <?php endif; ?>
            
            <div class="movie-excerpt">
                <?php echo wp_trim_words(get_the_excerpt(), 12, '...'); ?>
            </div>
        </div>
    </div>
</article>
