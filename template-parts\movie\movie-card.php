<?php
/**
 * Template part for displaying movie cards
 * 
 * @package DoMovie
 * @since 1.0.0
 */

$movie_id = get_the_ID();
$year = doomovie_get_movie_meta($movie_id, 'year');
$rating = doomovie_get_movie_meta($movie_id, 'rating');
$quality = doomovie_get_movie_meta($movie_id, 'quality');
$imdb_rating = doomovie_get_movie_meta($movie_id, 'imdb_rating');
$duration = doomovie_get_movie_meta($movie_id, 'duration');

// Get genres
$genres = get_the_terms($movie_id, 'movie_genre');
$genre_names = array();
if ($genres && !is_wp_error($genres)) {
    foreach ($genres as $genre) {
        $genre_names[] = $genre->name;
    }
}
?>

<div class="movie-card" data-movie-id="<?php echo esc_attr($movie_id); ?>">
    <div class="movie-poster">
        <a href="<?php the_permalink(); ?>" title="<?php the_title_attribute(); ?>">
            <?php if (has_post_thumbnail()) : ?>
                <?php the_post_thumbnail('movie-poster-medium', array('class' => 'poster-image', 'alt' => get_the_title())); ?>
            <?php else : ?>
                <img src="<?php echo esc_url(get_template_directory_uri() . '/assets/images/no-poster.jpg'); ?>" 
                     alt="<?php the_title_attribute(); ?>" 
                     class="poster-image no-poster">
            <?php endif; ?>
            
            <div class="movie-overlay">
                <div class="play-button">
                    <i class="fas fa-play"></i>
                </div>
                
                <div class="movie-info">
                    <?php if ($quality) : ?>
                        <span class="movie-quality"><?php echo esc_html($quality); ?></span>
                    <?php endif; ?>
                    
                    <?php if ($imdb_rating) : ?>
                        <span class="imdb-rating">
                            <i class="fas fa-star"></i>
                            <?php echo esc_html($imdb_rating); ?>
                        </span>
                    <?php endif; ?>
                </div>
            </div>
        </a>
        
        <div class="movie-actions">
            <?php if (is_user_logged_in()) : ?>
                <button class="btn-favorite <?php echo doomovie_is_favorite($movie_id) ? 'active' : ''; ?>" 
                        data-movie-id="<?php echo esc_attr($movie_id); ?>"
                        title="<?php _e('Add to Favorites', 'doomovie'); ?>">
                    <i class="fas fa-heart"></i>
                </button>
                
                <button class="btn-watch-later" 
                        data-movie-id="<?php echo esc_attr($movie_id); ?>"
                        title="<?php _e('Add to Watch Later', 'doomovie'); ?>">
                    <i class="fas fa-bookmark"></i>
                </button>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="movie-details">
        <h3 class="movie-title">
            <a href="<?php the_permalink(); ?>" title="<?php the_title_attribute(); ?>">
                <?php the_title(); ?>
            </a>
        </h3>
        
        <div class="movie-meta">
            <?php if ($year) : ?>
                <span class="movie-year"><?php echo esc_html($year); ?></span>
            <?php endif; ?>
            
            <?php if ($duration) : ?>
                <span class="movie-duration">
                    <i class="fas fa-clock"></i>
                    <?php echo esc_html($duration); ?> <?php _e('min', 'doomovie'); ?>
                </span>
            <?php endif; ?>
            
            <?php if ($rating) : ?>
                <span class="movie-rating"><?php echo esc_html($rating); ?></span>
            <?php endif; ?>
        </div>
        
        <?php if (!empty($genre_names)) : ?>
            <div class="movie-genres">
                <?php foreach (array_slice($genre_names, 0, 3) as $genre) : ?>
                    <span class="genre-tag"><?php echo esc_html($genre); ?></span>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        
        <?php if (has_excerpt()) : ?>
            <div class="movie-excerpt">
                <?php echo wp_trim_words(get_the_excerpt(), 15, '...'); ?>
            </div>
        <?php endif; ?>
    </div>
</div>
