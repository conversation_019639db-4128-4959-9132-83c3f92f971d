<?php
/**
 * Template part for displaying movie slider
 * 
 * @package DoMovie
 * @since 1.0.0
 */

// Get featured movies
$featured_count = get_theme_mod('doomovie_featured_count', 8);
$featured_movies = new WP_Query(array(
    'post_type' => 'movie',
    'posts_per_page' => $featured_count,
    'meta_query' => array(
        array(
            'key' => '_featured_movie',
            'value' => '1',
            'compare' => '='
        )
    ),
    'orderby' => 'date',
    'order' => 'DESC'
));

if (!$featured_movies->have_posts()) {
    // Fallback to latest movies if no featured movies
    $featured_movies = new WP_Query(array(
        'post_type' => 'movie',
        'posts_per_page' => $featured_count,
        'orderby' => 'date',
        'order' => 'DESC'
    ));
}
?>

<?php if ($featured_movies->have_posts()) : ?>
<div class="hero-slider-container">
    <div class="hero-slider" id="heroSlider">
        <?php while ($featured_movies->have_posts()) : $featured_movies->the_post(); ?>
            <?php
            $movie_id = get_the_ID();
            $year = doomovie_get_movie_meta($movie_id, 'year');
            $rating = doomovie_get_movie_meta($movie_id, 'rating');
            $quality = doomovie_get_movie_meta($movie_id, 'quality');
            $imdb_rating = doomovie_get_movie_meta($movie_id, 'imdb_rating');
            $duration = doomovie_get_movie_meta($movie_id, 'duration');
            $director = doomovie_get_movie_meta($movie_id, 'director');
            
            // Get genres
            $genres = get_the_terms($movie_id, 'movie_genre');
            $genre_names = array();
            if ($genres && !is_wp_error($genres)) {
                foreach ($genres as $genre) {
                    $genre_names[] = $genre->name;
                }
            }
            
            $background_image = '';
            if (has_post_thumbnail()) {
                $background_image = get_the_post_thumbnail_url($movie_id, 'hero-slider');
            }
            ?>
            
            <div class="hero-slide" style="background-image: url('<?php echo esc_url($background_image); ?>');">
                <div class="hero-overlay"></div>
                <div class="container">
                    <div class="hero-content">
                        <div class="hero-info">
                            <div class="hero-badges">
                                <?php if ($quality) : ?>
                                    <span class="quality-badge"><?php echo esc_html($quality); ?></span>
                                <?php endif; ?>
                                
                                <?php if ($imdb_rating) : ?>
                                    <span class="imdb-badge">
                                        <i class="fas fa-star"></i>
                                        <?php echo esc_html($imdb_rating); ?>
                                    </span>
                                <?php endif; ?>
                                
                                <?php if ($rating) : ?>
                                    <span class="rating-badge"><?php echo esc_html($rating); ?></span>
                                <?php endif; ?>
                            </div>
                            
                            <h1 class="hero-title">
                                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                            </h1>
                            
                            <div class="hero-meta">
                                <?php if ($year) : ?>
                                    <span class="meta-item"><?php echo esc_html($year); ?></span>
                                <?php endif; ?>
                                
                                <?php if ($duration) : ?>
                                    <span class="meta-item">
                                        <i class="fas fa-clock"></i>
                                        <?php echo esc_html($duration); ?> <?php _e('min', 'doomovie'); ?>
                                    </span>
                                <?php endif; ?>
                                
                                <?php if (!empty($genre_names)) : ?>
                                    <span class="meta-item">
                                        <?php echo implode(', ', array_slice($genre_names, 0, 3)); ?>
                                    </span>
                                <?php endif; ?>
                                
                                <?php if ($director) : ?>
                                    <span class="meta-item">
                                        <?php _e('Director:', 'doomovie'); ?> <?php echo esc_html($director); ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                            
                            <?php if (has_excerpt()) : ?>
                                <div class="hero-description">
                                    <?php echo wp_trim_words(get_the_excerpt(), 30, '...'); ?>
                                </div>
                            <?php endif; ?>
                            
                            <div class="hero-actions">
                                <a href="<?php the_permalink(); ?>" class="btn btn-primary btn-watch">
                                    <i class="fas fa-play"></i>
                                    <?php _e('Watch Now', 'doomovie'); ?>
                                </a>
                                
                                <a href="<?php the_permalink(); ?>" class="btn btn-secondary btn-info">
                                    <i class="fas fa-info-circle"></i>
                                    <?php _e('More Info', 'doomovie'); ?>
                                </a>
                                
                                <?php if (is_user_logged_in()) : ?>
                                    <button class="btn btn-outline btn-favorite <?php echo doomovie_is_favorite($movie_id) ? 'active' : ''; ?>" 
                                            data-movie-id="<?php echo esc_attr($movie_id); ?>">
                                        <i class="fas fa-heart"></i>
                                        <?php _e('Favorite', 'doomovie'); ?>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="hero-poster">
                            <?php if (has_post_thumbnail()) : ?>
                                <?php the_post_thumbnail('movie-poster-large', array('class' => 'hero-poster-image')); ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endwhile; ?>
    </div>
    
    <!-- Slider Navigation -->
    <div class="slider-nav">
        <button class="slider-btn slider-prev" id="sliderPrev">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="slider-btn slider-next" id="sliderNext">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>
    
    <!-- Slider Indicators -->
    <div class="slider-indicators">
        <?php 
        $slide_count = $featured_movies->post_count;
        for ($i = 0; $i < $slide_count; $i++) : 
        ?>
            <button class="slider-indicator <?php echo $i === 0 ? 'active' : ''; ?>" 
                    data-slide="<?php echo $i; ?>"></button>
        <?php endfor; ?>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const slider = document.getElementById('heroSlider');
    const slides = slider.querySelectorAll('.hero-slide');
    const prevBtn = document.getElementById('sliderPrev');
    const nextBtn = document.getElementById('sliderNext');
    const indicators = document.querySelectorAll('.slider-indicator');
    
    let currentSlide = 0;
    const totalSlides = slides.length;
    
    function showSlide(index) {
        slides.forEach((slide, i) => {
            slide.style.display = i === index ? 'block' : 'none';
        });
        
        indicators.forEach((indicator, i) => {
            indicator.classList.toggle('active', i === index);
        });
        
        currentSlide = index;
    }
    
    function nextSlide() {
        currentSlide = (currentSlide + 1) % totalSlides;
        showSlide(currentSlide);
    }
    
    function prevSlide() {
        currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
        showSlide(currentSlide);
    }
    
    // Event listeners
    nextBtn.addEventListener('click', nextSlide);
    prevBtn.addEventListener('click', prevSlide);
    
    indicators.forEach((indicator, index) => {
        indicator.addEventListener('click', () => showSlide(index));
    });
    
    // Auto-play
    setInterval(nextSlide, 5000);
    
    // Initialize
    showSlide(0);
});
</script>
<?php endif; ?>

<?php wp_reset_postdata(); ?>
