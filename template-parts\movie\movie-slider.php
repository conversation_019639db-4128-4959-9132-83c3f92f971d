<?php
/**
 * Hero Movie Slider Template
 * Featured movies slider for homepage
 *
 * @package DoMovie
 */

// Get featured movies for slider
$slider_movies = new WP_Query(array(
    'post_type' => 'movie',
    'posts_per_page' => 5,
    'meta_query' => array(
        array(
            'key' => '_featured_movie',
            'value' => '1',
            'compare' => '='
        )
    ),
    'orderby' => 'date',
    'order' => 'DESC'
));

// Fallback to latest movies if no featured movies
if (!$slider_movies->have_posts()) {
    $slider_movies = new WP_Query(array(
        'post_type' => 'movie',
        'posts_per_page' => 5,
        'orderby' => 'date',
        'order' => 'DESC'
    ));
}

if ($slider_movies->have_posts()) :
?>
<div class="hero-slider-container">
    <div class="hero-slider">
        <?php
        $slide_index = 0;
        while ($slider_movies->have_posts()) :
            $slider_movies->the_post();
            $movie_id = get_the_ID();
            $poster_url = get_post_meta($movie_id, '_movie_poster', true);
            $year = get_post_meta($movie_id, '_movie_year', true);
            $rating = get_post_meta($movie_id, '_movie_rating', true);
            $quality = get_post_meta($movie_id, '_movie_quality', true);
            $imdb_rating = get_post_meta($movie_id, '_movie_imdb_rating', true);
            $duration = get_post_meta($movie_id, '_movie_duration', true);
            $genres = wp_get_post_terms($movie_id, 'movie_genre');
            
            // Use poster as background if available
            $bg_image = $poster_url ? $poster_url : get_template_directory_uri() . '/assets/images/hero-bg.jpg';
        ?>
            <div class="hero-slide <?php echo $slide_index === 0 ? 'active' : ''; ?>" 
                 style="background-image: url('<?php echo esc_url($bg_image); ?>')">
                
                <div class="hero-overlay"></div>
                
                <div class="container">
                    <div class="hero-content">
                        <div class="hero-info">
                            
                            <!-- Movie Badges -->
                            <div class="hero-badges">
                                <?php if ($quality) : ?>
                                    <span class="quality-badge"><?php echo esc_html($quality); ?></span>
                                <?php endif; ?>
                                
                                <?php if ($imdb_rating) : ?>
                                    <span class="imdb-badge">IMDb <?php echo esc_html($imdb_rating); ?></span>
                                <?php endif; ?>
                                
                                <?php if ($rating) : ?>
                                    <span class="rating-badge"><?php echo esc_html($rating); ?></span>
                                <?php endif; ?>
                            </div>

                            <!-- Movie Title -->
                            <h1 class="hero-title text-reveal glitch-effect" data-text="<?php the_title_attribute(); ?>"><?php the_title(); ?></h1>

                            <!-- Movie Meta -->
                            <div class="hero-meta">
                                <?php if ($year) : ?>
                                    <div class="meta-item">
                                        <i class="fas fa-calendar"></i>
                                        <span><?php echo esc_html($year); ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($duration) : ?>
                                    <div class="meta-item">
                                        <i class="fas fa-clock"></i>
                                        <span><?php echo esc_html($duration); ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($genres)) : ?>
                                    <div class="meta-item">
                                        <i class="fas fa-tags"></i>
                                        <span>
                                            <?php 
                                            $genre_names = array();
                                            foreach (array_slice($genres, 0, 2) as $genre) {
                                                $genre_names[] = $genre->name;
                                            }
                                            echo implode(', ', $genre_names);
                                            ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Movie Description -->
                            <div class="hero-description">
                                <?php echo wp_trim_words(get_the_excerpt(), 30, '...'); ?>
                            </div>

                            <!-- Action Buttons -->
                            <div class="hero-actions">
                                <a href="<?php the_permalink(); ?>" class="btn btn-primary liquid-button glow-animation">
                                    <i class="fas fa-play"></i>
                                    <?php _e('Watch Now', 'doomovie'); ?>
                                </a>

                                <button class="btn btn-secondary btn-favorite magnetic-button" data-movie-id="<?php echo esc_attr($movie_id); ?>">
                                    <i class="far fa-heart"></i>
                                    <?php _e('Add to Favorites', 'doomovie'); ?>
                                </button>

                                <a href="<?php the_permalink(); ?>#trailer" class="btn btn-outline ripple">
                                    <i class="fas fa-info-circle"></i>
                                    <?php _e('More Info', 'doomovie'); ?>
                                </a>
                            </div>
                        </div>

                        <!-- Movie Poster -->
                        <?php if ($poster_url) : ?>
                            <div class="hero-poster parallax-element float-animation" data-speed="0.3">
                                <img src="<?php echo esc_url($poster_url); ?>"
                                     alt="<?php the_title_attribute(); ?>"
                                     class="hero-poster-image morphing-shape"
                                     loading="lazy">
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php
            $slide_index++;
        endwhile;
        wp_reset_postdata();
        ?>
    </div>

    <!-- Slider Navigation -->
    <button class="slider-nav slider-prev" aria-label="<?php _e('Previous slide', 'doomovie'); ?>">
        <i class="fas fa-chevron-left"></i>
    </button>
    
    <button class="slider-nav slider-next" aria-label="<?php _e('Next slide', 'doomovie'); ?>">
        <i class="fas fa-chevron-right"></i>
    </button>

    <!-- Slider Indicators -->
    <div class="slider-indicators">
        <?php for ($i = 0; $i < $slide_index; $i++) : ?>
            <button class="slider-indicator <?php echo $i === 0 ? 'active' : ''; ?>" 
                    data-slide="<?php echo $i; ?>"
                    aria-label="<?php printf(__('Go to slide %d', 'doomovie'), $i + 1); ?>">
            </button>
        <?php endfor; ?>
    </div>
</div>

<?php endif; ?>
