<?php
/**
 * Template part for displaying Top 10 movies
 * Inspired by ezmovie.me layout
 *
 * @package DoMovie
 */

// Get top 10 movies based on views or ratings
$top_movies = new WP_Query(array(
    'post_type' => 'movie',
    'posts_per_page' => 10,
    'meta_key' => '_movie_views',
    'orderby' => 'meta_value_num',
    'order' => 'DESC',
    'meta_query' => array(
        array(
            'key' => '_movie_views',
            'value' => 0,
            'compare' => '>',
            'type' => 'NUMERIC'
        )
    )
));

// Fallback to latest movies if no views data
if (!$top_movies->have_posts()) {
    $top_movies = new WP_Query(array(
        'post_type' => 'movie',
        'posts_per_page' => 10,
        'orderby' => 'date',
        'order' => 'DESC'
    ));
}

if ($top_movies->have_posts()) :
?>
<section class="top-10-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title"><?php _e('Top 10 Movies', 'doomovie'); ?></h2>
        </div>
        
        <div class="top-10-grid">
            <div class="top-10-column">
                <?php
                $counter = 1;
                while ($top_movies->have_posts() && $counter <= 5) :
                    $top_movies->the_post();
                    $movie_id = get_the_ID();
                    $poster_url = get_post_meta($movie_id, '_movie_poster', true);
                    $year = get_post_meta($movie_id, '_movie_year', true);
                    $rating = get_post_meta($movie_id, '_movie_rating', true);
                    $quality = get_post_meta($movie_id, '_movie_quality', true);
                    $views = get_post_meta($movie_id, '_movie_views', true);
                ?>
                    <div class="top-10-item" data-movie-id="<?php echo esc_attr($movie_id); ?>">
                        <div class="top-10-number"><?php echo $counter; ?></div>
                        
                        <div class="top-10-poster">
                            <a href="<?php the_permalink(); ?>">
                                <?php if ($poster_url) : ?>
                                    <img src="<?php echo esc_url($poster_url); ?>" 
                                         alt="<?php the_title_attribute(); ?>"
                                         loading="lazy">
                                <?php else : ?>
                                    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/no-poster.jpg" 
                                         alt="<?php the_title_attribute(); ?>"
                                         loading="lazy">
                                <?php endif; ?>
                            </a>
                        </div>
                        
                        <div class="top-10-info">
                            <h4>
                                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                            </h4>
                            <div class="meta">
                                <?php if ($year) : ?>
                                    <span class="year"><?php echo esc_html($year); ?></span>
                                <?php endif; ?>
                                
                                <?php if ($quality) : ?>
                                    <span class="quality"><?php echo esc_html($quality); ?></span>
                                <?php endif; ?>
                                
                                <?php if ($rating) : ?>
                                    <span class="rating">⭐ <?php echo esc_html($rating); ?></span>
                                <?php endif; ?>
                                
                                <?php if ($views) : ?>
                                    <span class="views"><?php echo number_format($views); ?> views</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php
                    $counter++;
                endwhile;
                ?>
            </div>
            
            <div class="top-10-column">
                <?php
                while ($top_movies->have_posts() && $counter <= 10) :
                    $top_movies->the_post();
                    $movie_id = get_the_ID();
                    $poster_url = get_post_meta($movie_id, '_movie_poster', true);
                    $year = get_post_meta($movie_id, '_movie_year', true);
                    $rating = get_post_meta($movie_id, '_movie_rating', true);
                    $quality = get_post_meta($movie_id, '_movie_quality', true);
                    $views = get_post_meta($movie_id, '_movie_views', true);
                ?>
                    <div class="top-10-item" data-movie-id="<?php echo esc_attr($movie_id); ?>">
                        <div class="top-10-number"><?php echo $counter; ?></div>
                        
                        <div class="top-10-poster">
                            <a href="<?php the_permalink(); ?>">
                                <?php if ($poster_url) : ?>
                                    <img src="<?php echo esc_url($poster_url); ?>" 
                                         alt="<?php the_title_attribute(); ?>"
                                         loading="lazy">
                                <?php else : ?>
                                    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/no-poster.jpg" 
                                         alt="<?php the_title_attribute(); ?>"
                                         loading="lazy">
                                <?php endif; ?>
                            </a>
                        </div>
                        
                        <div class="top-10-info">
                            <h4>
                                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                            </h4>
                            <div class="meta">
                                <?php if ($year) : ?>
                                    <span class="year"><?php echo esc_html($year); ?></span>
                                <?php endif; ?>
                                
                                <?php if ($quality) : ?>
                                    <span class="quality"><?php echo esc_html($quality); ?></span>
                                <?php endif; ?>
                                
                                <?php if ($rating) : ?>
                                    <span class="rating">⭐ <?php echo esc_html($rating); ?></span>
                                <?php endif; ?>
                                
                                <?php if ($views) : ?>
                                    <span class="views"><?php echo number_format($views); ?> views</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php
                    $counter++;
                endwhile;
                wp_reset_postdata();
                ?>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>
