<?php
/**
 * Main Navigation Template
 * Modern navigation inspired by ezmovie.me
 *
 * @package DoMovie
 */
?>

<nav class="main-navigation" id="main-navigation">
    <div class="container">
        <div class="nav-wrapper">
            
            <!-- Site Logo - ล้ำและสวยงาม -->
            <div class="site-logo-modern">
                <a href="<?php echo esc_url(home_url('/')); ?>" rel="home" class="logo-link-modern">
                    <div class="logo-container">
                        <div class="logo-icon">
                            <i class="fas fa-film"></i>
                            <div class="logo-glow"></div>
                        </div>
                        <div class="logo-text">
                            <h1 class="site-title-modern">
                                <span class="title-main">
                                    <?php
                                    $site_name = get_bloginfo('name');
                                    if (empty($site_name)) {
                                        $site_name = 'DoMovie';
                                    }
                                    echo esc_html($site_name);
                                    ?>
                                </span>
                                <span class="title-subtitle">CINEMA</span>
                            </h1>
                            <div class="logo-tagline">Premium Movie Experience</div>
                        </div>
                    </div>
                    <div class="logo-particles">
                        <div class="particle particle-1"></div>
                        <div class="particle particle-2"></div>
                        <div class="particle particle-3"></div>
                    </div>
                </a>
            </div>

            <!-- Desktop Navigation Menu -->
            <div class="nav-menu-wrapper">
                <ul class="nav-menu">
                    <li class="current-menu-item">
                        <a href="<?php echo esc_url(home_url('/')); ?>"><?php _e('หน้าหลัก', 'doomovie'); ?></a>
                    </li>
                    <li class="menu-item-has-children">
                        <a href="#" class="dropdown-toggle">
                            <?php _e('หมวดหมู่', 'doomovie'); ?>
                            <i class="fas fa-chevron-down"></i>
                        </a>
                        <div class="mega-menu-dropdown">
                            <div class="mega-menu-content">
                                <div class="mega-menu-column">
                                    <h4 class="mega-menu-title">
                                        <i class="fas fa-star"></i>
                                        <?php _e('หนังใหม่', 'doomovie'); ?>
                                    </h4>
                                    <ul class="mega-menu-list">
                                        <li><a href="<?php echo esc_url(home_url('/category/new-movies/')); ?>"><?php _e('หนังใหม่ล่าสุด', 'doomovie'); ?></a></li>
                                        <li><a href="<?php echo esc_url(home_url('/category/coming-soon/')); ?>"><?php _e('หนังกำลังจะมา', 'doomovie'); ?></a></li>
                                        <li><a href="<?php echo esc_url(home_url('/category/trending/')); ?>"><?php _e('หนังกำลังฮิต', 'doomovie'); ?></a></li>
                                    </ul>
                                </div>

                                <div class="mega-menu-column">
                                    <h4 class="mega-menu-title">
                                        <i class="fas fa-tv"></i>
                                        <?php _e('ซีรี่ย์', 'doomovie'); ?>
                                    </h4>
                                    <ul class="mega-menu-list">
                                        <li><a href="<?php echo esc_url(home_url('/category/series/')); ?>"><?php _e('ซีรี่ย์ทั้งหมด', 'doomovie'); ?></a></li>
                                        <li><a href="<?php echo esc_url(home_url('/category/korean-series/')); ?>"><?php _e('ซีรี่ย์เกาหลี', 'doomovie'); ?></a></li>
                                        <li><a href="<?php echo esc_url(home_url('/category/thai-series/')); ?>"><?php _e('ซีรี่ย์ไทย', 'doomovie'); ?></a></li>
                                    </ul>
                                </div>

                                <div class="mega-menu-column">
                                    <h4 class="mega-menu-title">
                                        <i class="fas fa-flag"></i>
                                        <?php _e('หนังไทย', 'doomovie'); ?>
                                    </h4>
                                    <ul class="mega-menu-list">
                                        <li><a href="<?php echo esc_url(home_url('/category/thai-movies/')); ?>"><?php _e('หนังไทยทั้งหมด', 'doomovie'); ?></a></li>
                                        <li><a href="<?php echo esc_url(home_url('/category/thai-comedy/')); ?>"><?php _e('หนังตลกไทย', 'doomovie'); ?></a></li>
                                        <li><a href="<?php echo esc_url(home_url('/category/thai-horror/')); ?>"><?php _e('หนังผีไทย', 'doomovie'); ?></a></li>
                                    </ul>
                                </div>

                                <div class="mega-menu-column">
                                    <h4 class="mega-menu-title">
                                        <i class="fas fa-mask"></i>
                                        <?php _e('หนังฮีโร่', 'doomovie'); ?>
                                    </h4>
                                    <ul class="mega-menu-list">
                                        <li><a href="<?php echo esc_url(home_url('/category/hero-movies/')); ?>"><?php _e('ซูเปอร์ฮีโร่', 'doomovie'); ?></a></li>
                                        <li><a href="<?php echo esc_url(home_url('/category/marvel/')); ?>"><?php _e('Marvel', 'doomovie'); ?></a></li>
                                        <li><a href="<?php echo esc_url(home_url('/category/dc/')); ?>"><?php _e('DC Comics', 'doomovie'); ?></a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </li>
                    <li><a href="<?php echo esc_url(home_url('/top-rated/')); ?>"><?php _e('หนังยอดนิยม', 'doomovie'); ?></a></li>
                    <li><a href="<?php echo esc_url(home_url('/genres/')); ?>"><?php _e('แนวหนัง', 'doomovie'); ?></a></li>
                    <li><a href="<?php echo esc_url(home_url('/contact/')); ?>"><?php _e('ติดต่อ', 'doomovie'); ?></a></li>
                </ul>
            </div>

            <!-- Navigation Actions -->
            <div class="nav-actions">
                
                <!-- Search -->
                <div class="search-wrapper">
                    <button class="search-toggle" aria-label="<?php _e('Search', 'doomovie'); ?>">
                        <i class="fas fa-search"></i>
                    </button>
                    
                    <div class="search-form-wrapper">
                        <form class="search-form" role="search" method="get" action="<?php echo esc_url(home_url('/')); ?>">
                            <input type="search" 
                                   class="search-field" 
                                   placeholder="<?php _e('Search movies...', 'doomovie'); ?>" 
                                   value="<?php echo get_search_query(); ?>" 
                                   name="s" 
                                   autocomplete="off">
                            <button type="submit" class="search-submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                        <div class="search-results"></div>
                    </div>
                </div>

                <!-- User Menu -->
                <?php if (is_user_logged_in()) : ?>
                    <div class="user-menu">
                        <button class="user-toggle" aria-label="<?php _e('User Menu', 'doomovie'); ?>">
                            <i class="fas fa-user"></i>
                        </button>
                        
                        <div class="user-dropdown-menu">
                            <div class="user-info">
                                <span class="user-name"><?php echo wp_get_current_user()->display_name; ?></span>
                            </div>
                            <ul class="user-menu-items">
                                <li><a href="<?php echo esc_url(home_url('/my-favorites/')); ?>">
                                    <i class="fas fa-heart"></i> <?php _e('My Favorites', 'doomovie'); ?>
                                </a></li>
                                <li><a href="<?php echo esc_url(home_url('/watch-later/')); ?>">
                                    <i class="fas fa-bookmark"></i> <?php _e('Watch Later', 'doomovie'); ?>
                                </a></li>
                                <li><a href="<?php echo esc_url(wp_logout_url(home_url())); ?>">
                                    <i class="fas fa-sign-out-alt"></i> <?php _e('Logout', 'doomovie'); ?>
                                </a></li>
                            </ul>
                        </div>
                    </div>
                <?php else : ?>
                    <div class="auth-buttons">
                        <a href="<?php echo esc_url(wp_login_url()); ?>" class="btn btn-outline btn-sm">
                            <?php _e('Login', 'doomovie'); ?>
                        </a>
                    </div>
                <?php endif; ?>

                <!-- Mobile Menu Toggle -->
                <button class="mobile-menu-toggle" aria-label="<?php _e('Mobile Menu', 'doomovie'); ?>">
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile Menu -->
    <div class="mobile-menu">
        <div class="mobile-nav-menu">
            <?php
            wp_nav_menu(array(
                'theme_location' => 'primary',
                'menu_class'     => 'mobile-nav-menu',
                'container'      => false,
                'fallback_cb'    => 'doomovie_fallback_menu',
            ));
            ?>
        </div>
    </div>
</nav>

<?php
/**
 * Fallback menu if no menu is assigned
 */
function doomovie_fallback_menu() {
    echo '<ul class="nav-menu">';
    echo '<li class="current-menu-item"><a href="' . esc_url(home_url('/')) . '">' . __('Home', 'doomovie') . '</a></li>';
    echo '<li><a href="' . esc_url(home_url('/movies/')) . '">' . __('Movies', 'doomovie') . '</a></li>';
    echo '<li><a href="' . esc_url(home_url('/genres/')) . '">' . __('Genres', 'doomovie') . '</a></li>';
    echo '<li><a href="' . esc_url(home_url('/top-rated/')) . '">' . __('Top Rated', 'doomovie') . '</a></li>';
    echo '<li><a href="' . esc_url(home_url('/contact/')) . '">' . __('Contact', 'doomovie') . '</a></li>';
    echo '</ul>';
}
?>
