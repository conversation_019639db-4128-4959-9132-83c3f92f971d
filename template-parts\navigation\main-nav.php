<?php
/**
 * Template part for displaying main navigation
 * 
 * @package DoMovie
 * @since 1.0.0
 */
?>

<nav class="main-navigation" id="mainNav">
    <div class="container">
        <div class="nav-wrapper">
            <!-- Logo -->
            <div class="site-logo">
                <?php if (has_custom_logo()) : ?>
                    <?php the_custom_logo(); ?>
                <?php else : ?>
                    <a href="<?php echo esc_url(home_url('/')); ?>" class="logo-text">
                        <?php bloginfo('name'); ?>
                    </a>
                <?php endif; ?>
            </div>
            
            <!-- Primary Menu -->
            <div class="nav-menu-wrapper">
                <?php
                wp_nav_menu(array(
                    'theme_location' => 'primary',
                    'menu_class' => 'nav-menu',
                    'container' => false,
                    'fallback_cb' => 'doomovie_fallback_menu'
                ));
                ?>
            </div>
            
            <!-- Search & User Actions -->
            <div class="nav-actions">
                <!-- Search Form -->
                <div class="search-wrapper">
                    <button class="search-toggle" id="searchToggle">
                        <i class="fas fa-search"></i>
                    </button>
                    <div class="search-form-wrapper" id="searchForm">
                        <form role="search" method="get" class="search-form" action="<?php echo esc_url(home_url('/')); ?>">
                            <input type="search" 
                                   class="search-field" 
                                   placeholder="<?php _e('Search movies...', 'doomovie'); ?>" 
                                   value="<?php echo get_search_query(); ?>" 
                                   name="s" 
                                   autocomplete="off">
                            <input type="hidden" name="post_type" value="movie">
                            <button type="submit" class="search-submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                        <div class="search-results" id="searchResults"></div>
                    </div>
                </div>
                
                <!-- User Menu -->
                <div class="user-menu">
                    <?php if (is_user_logged_in()) : ?>
                        <div class="user-dropdown">
                            <button class="user-toggle" id="userToggle">
                                <?php echo get_avatar(get_current_user_id(), 32); ?>
                                <span class="user-name"><?php echo wp_get_current_user()->display_name; ?></span>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="user-dropdown-menu" id="userDropdown">
                                <a href="<?php echo esc_url(home_url('/my-account/')); ?>" class="dropdown-item">
                                    <i class="fas fa-user"></i>
                                    <?php _e('My Account', 'doomovie'); ?>
                                </a>
                                <a href="<?php echo esc_url(home_url('/favorites/')); ?>" class="dropdown-item">
                                    <i class="fas fa-heart"></i>
                                    <?php _e('Favorites', 'doomovie'); ?>
                                </a>
                                <a href="<?php echo esc_url(home_url('/watch-later/')); ?>" class="dropdown-item">
                                    <i class="fas fa-bookmark"></i>
                                    <?php _e('Watch Later', 'doomovie'); ?>
                                </a>
                                <a href="<?php echo esc_url(home_url('/history/')); ?>" class="dropdown-item">
                                    <i class="fas fa-history"></i>
                                    <?php _e('History', 'doomovie'); ?>
                                </a>
                                <div class="dropdown-divider"></div>
                                <a href="<?php echo wp_logout_url(home_url()); ?>" class="dropdown-item">
                                    <i class="fas fa-sign-out-alt"></i>
                                    <?php _e('Logout', 'doomovie'); ?>
                                </a>
                            </div>
                        </div>
                    <?php else : ?>
                        <div class="auth-buttons">
                            <a href="<?php echo wp_login_url(); ?>" class="btn btn-outline btn-login">
                                <i class="fas fa-sign-in-alt"></i>
                                <?php _e('Login', 'doomovie'); ?>
                            </a>
                            <a href="<?php echo wp_registration_url(); ?>" class="btn btn-primary btn-register">
                                <i class="fas fa-user-plus"></i>
                                <?php _e('Register', 'doomovie'); ?>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Mobile Menu Toggle -->
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                </button>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <div class="mobile-menu" id="mobileMenu">
            <?php
            wp_nav_menu(array(
                'theme_location' => 'mobile',
                'menu_class' => 'mobile-nav-menu',
                'container' => false,
                'fallback_cb' => 'doomovie_fallback_mobile_menu'
            ));
            ?>
            
            <div class="mobile-auth">
                <?php if (is_user_logged_in()) : ?>
                    <div class="mobile-user-info">
                        <?php echo get_avatar(get_current_user_id(), 48); ?>
                        <span class="mobile-user-name"><?php echo wp_get_current_user()->display_name; ?></span>
                    </div>
                    <div class="mobile-user-links">
                        <a href="<?php echo esc_url(home_url('/my-account/')); ?>">
                            <i class="fas fa-user"></i> <?php _e('My Account', 'doomovie'); ?>
                        </a>
                        <a href="<?php echo esc_url(home_url('/favorites/')); ?>">
                            <i class="fas fa-heart"></i> <?php _e('Favorites', 'doomovie'); ?>
                        </a>
                        <a href="<?php echo wp_logout_url(home_url()); ?>">
                            <i class="fas fa-sign-out-alt"></i> <?php _e('Logout', 'doomovie'); ?>
                        </a>
                    </div>
                <?php else : ?>
                    <div class="mobile-auth-buttons">
                        <a href="<?php echo wp_login_url(); ?>" class="btn btn-outline">
                            <?php _e('Login', 'doomovie'); ?>
                        </a>
                        <a href="<?php echo wp_registration_url(); ?>" class="btn btn-primary">
                            <?php _e('Register', 'doomovie'); ?>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</nav>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchToggle = document.getElementById('searchToggle');
    const searchForm = document.getElementById('searchForm');
    const searchField = searchForm.querySelector('.search-field');
    const searchResults = document.getElementById('searchResults');
    
    searchToggle.addEventListener('click', function() {
        searchForm.classList.toggle('active');
        if (searchForm.classList.contains('active')) {
            searchField.focus();
        }
    });
    
    // Live search
    let searchTimeout;
    searchField.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();
        
        if (query.length >= 3) {
            searchTimeout = setTimeout(() => {
                performLiveSearch(query);
            }, 300);
        } else {
            searchResults.innerHTML = '';
            searchResults.style.display = 'none';
        }
    });
    
    function performLiveSearch(query) {
        fetch(doomovie_ajax.ajax_url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'search_movies',
                search_term: query,
                nonce: doomovie_ajax.nonce
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data.length > 0) {
                displaySearchResults(data.data);
            } else {
                searchResults.innerHTML = '<div class="no-results">No movies found</div>';
                searchResults.style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Search error:', error);
        });
    }
    
    function displaySearchResults(results) {
        let html = '';
        results.forEach(movie => {
            html += `
                <div class="search-result-item">
                    <img src="${movie.poster}" alt="${movie.title}" class="result-poster">
                    <div class="result-info">
                        <h4><a href="${movie.url}">${movie.title}</a></h4>
                        <div class="result-meta">
                            ${movie.year ? `<span>${movie.year}</span>` : ''}
                            ${movie.quality ? `<span class="quality">${movie.quality}</span>` : ''}
                            ${movie.rating ? `<span class="rating">${movie.rating}</span>` : ''}
                        </div>
                    </div>
                </div>
            `;
        });
        searchResults.innerHTML = html;
        searchResults.style.display = 'block';
    }
    
    // User dropdown
    const userToggle = document.getElementById('userToggle');
    const userDropdown = document.getElementById('userDropdown');
    
    if (userToggle) {
        userToggle.addEventListener('click', function() {
            userDropdown.classList.toggle('active');
        });
    }
    
    // Mobile menu
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const mobileMenu = document.getElementById('mobileMenu');
    
    mobileMenuToggle.addEventListener('click', function() {
        this.classList.toggle('active');
        mobileMenu.classList.toggle('active');
        document.body.classList.toggle('mobile-menu-open');
    });
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.search-wrapper')) {
            searchForm.classList.remove('active');
            searchResults.style.display = 'none';
        }
        
        if (!e.target.closest('.user-dropdown')) {
            if (userDropdown) userDropdown.classList.remove('active');
        }
    });
});
</script>
