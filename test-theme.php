<?php
/**
 * Test file to check theme functionality
 * 
 * @package DoMovie
 * @since 1.0.0
 */

// Include WordPress
require_once('../../../wp-load.php');

// Test basic functions
echo "<h1>DoMovie Theme Test</h1>";

echo "<h2>Theme Information</h2>";
echo "Theme Name: " . wp_get_theme()->get('Name') . "<br>";
echo "Theme Version: " . wp_get_theme()->get('Version') . "<br>";
echo "Theme Path: " . get_template_directory() . "<br>";
echo "Theme URL: " . get_template_directory_uri() . "<br>";

echo "<h2>Required Files Check</h2>";
$required_files = array(
    'functions.php',
    'style.css',
    'index.php',
    'header.php',
    'footer.php',
    'inc/custom-post-types.php',
    'inc/custom-taxonomies.php',
    'inc/enqueue-scripts.php',
    'inc/custom-fields.php',
    'inc/theme-functions.php',
    'inc/ajax-handlers.php',
    'inc/user-functions.php',
    'inc/admin-functions.php',
    'inc/customizer.php',
    'inc/security.php'
);

foreach ($required_files as $file) {
    $file_path = get_template_directory() . '/' . $file;
    if (file_exists($file_path)) {
        echo "✅ " . $file . " - EXISTS<br>";
    } else {
        echo "❌ " . $file . " - MISSING<br>";
    }
}

echo "<h2>Function Check</h2>";
$functions_to_check = array(
    'doomovie_theme_setup',
    'doomovie_widgets_init',
    'doomovie_excerpt_length',
    'doomovie_excerpt_more',
    'doomovie_body_classes',
    'doomovie_get_movie_meta'
);

foreach ($functions_to_check as $function) {
    if (function_exists($function)) {
        echo "✅ " . $function . "() - EXISTS<br>";
    } else {
        echo "❌ " . $function . "() - MISSING<br>";
    }
}

echo "<h2>Post Type Check</h2>";
if (post_type_exists('movie')) {
    echo "✅ Movie post type - EXISTS<br>";
    
    // Get movie post type object
    $movie_post_type = get_post_type_object('movie');
    echo "Movie post type label: " . $movie_post_type->labels->name . "<br>";
} else {
    echo "❌ Movie post type - MISSING<br>";
}

echo "<h2>Taxonomy Check</h2>";
$taxonomies = array('movie_genre', 'movie_year', 'movie_country', 'movie_quality', 'movie_language');
foreach ($taxonomies as $taxonomy) {
    if (taxonomy_exists($taxonomy)) {
        echo "✅ " . $taxonomy . " - EXISTS<br>";
    } else {
        echo "❌ " . $taxonomy . " - MISSING<br>";
    }
}

echo "<h2>Theme Support Check</h2>";
$theme_supports = array(
    'post-thumbnails',
    'title-tag',
    'html5',
    'custom-logo',
    'customize-selective-refresh-widgets'
);

foreach ($theme_supports as $support) {
    if (current_theme_supports($support)) {
        echo "✅ " . $support . " - SUPPORTED<br>";
    } else {
        echo "❌ " . $support . " - NOT SUPPORTED<br>";
    }
}

echo "<h2>Navigation Menus Check</h2>";
$nav_menus = get_registered_nav_menus();
if (!empty($nav_menus)) {
    foreach ($nav_menus as $location => $description) {
        echo "✅ " . $location . " - " . $description . "<br>";
    }
} else {
    echo "❌ No navigation menus registered<br>";
}

echo "<h2>Image Sizes Check</h2>";
global $_wp_additional_image_sizes;
if (!empty($_wp_additional_image_sizes)) {
    foreach ($_wp_additional_image_sizes as $size => $data) {
        echo "✅ " . $size . " - " . $data['width'] . "x" . $data['height'] . "<br>";
    }
} else {
    echo "❌ No custom image sizes found<br>";
}

echo "<h2>Test Complete</h2>";
echo "If you see mostly green checkmarks (✅), the theme is working correctly!";
?>
